using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Vuforia;

public class DetectionZonePanelManager : MonoBehaviour
{
    [Header("References")]
    public VuforiaDetectionZone detectionZone;
    public DBCoRe database;
    public Canvas uiCanvas;
    
    [Header("Panel Settings")]
    public GameObject panelPrefab;
    public Vector2 panelOffset = new Vector2(0, -100);
    
    [Header("Search Settings")]
    public GameObject searchUIPrefab;
    public Vector2 searchUIOffset = new Vector2(0, 0);

    [Header("Contextual Button Settings")]
    public bool enableContextualButton = true;

    [Header("Debug")]
    public bool enableDebugLogging = false;
    public ContextualLeftPanelButton contextualButton;
    public bool hideLeftPanelByDefault = true;

    [Header("Animation Integration")]
    public UIAnimationController animationController;

    private Dictionary<string, VuMarkDataPanel> zonePanels = new Dictionary<string, VuMarkDataPanel>();
    private Dictionary<string, string> zoneVuMarkData = new Dictionary<string, string>();
    private Dictionary<string, bool> lastVuMarkState = new Dictionary<string, bool>();
    private Dictionary<string, ProductData> zoneSearchData = new Dictionary<string, ProductData>();
    private Dictionary<string, ZoneSearchUI> zoneSearchUIs = new Dictionary<string, ZoneSearchUI>();
    private string leftZoneName = "";
    private bool leftPanelVisible = false;
    
    void Start()
    {
        if (detectionZone == null)
            detectionZone = FindObjectOfType<VuforiaDetectionZone>();
            
        if (database == null)
            database = FindObjectOfType<DBCoRe>();
            
        if (uiCanvas == null)
        {
            // Use stage 1 canvas from GameManager
            if (GameManager.Instance != null && GameManager.Instance.stageCanvases.Length > 0)
            {
                uiCanvas = GameManager.Instance.stageCanvases[0]; // Stage 1 canvas
            }
            else
            {
                uiCanvas = FindObjectOfType<Canvas>();
            }
        }

        // Auto-find animation controller if not assigned
        if (animationController == null)
        {
            animationController = FindObjectOfType<UIAnimationController>();
        }
            
        // Wait for zones to be created
        StartCoroutine(InitializeWhenReady());
    }
    
    System.Collections.IEnumerator InitializeWhenReady()
    {
        // Wait for detection zones to be created
        while (detectionZone == null || detectionZone.detectionZones.Count == 0)
        {
            yield return new WaitForSeconds(0.1f);
        }
        
        DetermineLeftZone();
        CreatePanelsForZones();
        CreateSearchUIForZones();
        SubscribeToVuMarkEvents();
        InitializeContextualButton();
    }
    
    void Update()
    {
        CheckVuMarkStates();
        CheckScreenSaverMode();
    }
    
    private bool wasInScreenSaverMode = false;
    
    void CheckScreenSaverMode()
    {
        if (detectionZone?.metaballController == null) return;
        
        bool isInScreenSaver = detectionZone.metaballController.IsInScreenSaverMode();
        
        if (isInScreenSaver && !wasInScreenSaverMode)
        {
            // Entering screensaver mode - hide all panels and clear data
            HideAllPanelsAndClearData();
        }
        
        wasInScreenSaverMode = isInScreenSaver;
    }

    void DetermineLeftZone()
    {
        if (detectionZone == null) return;

        // Get the left zone name from VuforiaDetectionZone
        leftZoneName = detectionZone.GetLeftZoneName();

        if (!string.IsNullOrEmpty(leftZoneName))
        {
            Debug.Log($"DetectionZonePanelManager: Left zone determined as '{leftZoneName}'");
        }
    }
    
    void HideAllPanelsAndClearData()
    {
        foreach (var panel in zonePanels.Values)
        {
            panel.Hide();
        }

        // Hide the left panel completely when entering screensaver mode
        if (hideLeftPanelByDefault)
        {
            HideLeftPanel();
        }

        zoneVuMarkData.Clear();
        zoneSearchData.Clear();

        Debug.Log("Cleared all panel data due to screensaver mode");
    }
    
    void CheckVuMarkStates()
    {
        var vuMarkBehaviours = FindObjectsOfType<VuMarkBehaviour>();
        var currentlyInZones = new HashSet<string>();
        
        foreach (var vuMark in vuMarkBehaviours)
        {
            if (vuMark == null) continue;
            
            string vuMarkId = vuMark.InstanceId?.ToString() ?? vuMark.TargetName;
            bool isTracked = vuMark.TargetStatus.Status == Vuforia.Status.TRACKED || 
                           vuMark.TargetStatus.Status == Vuforia.Status.EXTENDED_TRACKED;
            
            bool wasTracked = lastVuMarkState.ContainsKey(vuMarkId) && lastVuMarkState[vuMarkId];
            
            if (isTracked)
            {
                string currentZone = GetVuMarkZone(vuMark);
                if (!string.IsNullOrEmpty(currentZone))
                {
                    currentlyInZones.Add($"{currentZone}:{vuMarkId}");
                    
                    if (!wasTracked || !zoneVuMarkData.ContainsKey(currentZone) || zoneVuMarkData[currentZone] != vuMarkId)
                    {
                        OnVuMarkDetected(vuMark);
                    }
                }
            }
            
            if (!isTracked && wasTracked)
            {
                OnVuMarkLost(vuMark);
            }
            
            lastVuMarkState[vuMarkId] = isTracked;
        }
        
        // Hide panels for VuMarks no longer in zones (but keep search data)
        foreach (var kvp in zoneVuMarkData.ToArray())
        {
            if (!currentlyInZones.Contains($"{kvp.Key}:{kvp.Value}"))
            {
                if (zonePanels.ContainsKey(kvp.Key) && !zoneSearchData.ContainsKey(kvp.Key))
                {
                    zonePanels[kvp.Key].Hide();
                }
                zoneVuMarkData.Remove(kvp.Key);
            }
        }
    }
    
    void CreatePanelsForZones()
    {
        if (detectionZone == null || uiCanvas == null)
        {
            Debug.LogError("DetectionZone or Canvas is null");
            return;
        }
        
        if (panelPrefab == null)
        {
            Debug.LogError("Panel prefab is null - creating default panel");
            CreateDefaultPanel();
            return;
        }
        
        Debug.Log($"Creating panels for {detectionZone.detectionZones.Count} zones");
        
        foreach (var zone in detectionZone.detectionZones)
        {
            GameObject panelObj = Instantiate(panelPrefab, uiCanvas.transform);
            VuMarkDataPanel panel = panelObj.GetComponent<VuMarkDataPanel>();
            
            if (panel == null)
                panel = panelObj.AddComponent<VuMarkDataPanel>();
                
            zonePanels[zone.zoneName] = panel;
            
            // Position panel under the zone
            Vector2 zoneCenter = GetZoneCenterPosition(zone);
            panel.SetPosition(zoneCenter + panelOffset);

            panel.Hide();

            // Hide the left panel's GameObject entirely if hideLeftPanelByDefault is enabled
            if (hideLeftPanelByDefault && zone.zoneName == leftZoneName)
            {
                panelObj.SetActive(false);
                Debug.Log($"Left panel '{leftZoneName}' hidden by default");
            }
        }
    }
    
    void CreateSearchUIForZones()
    {
        if (detectionZone == null || uiCanvas == null)
        {
            Debug.LogError("DetectionZone or Canvas is null for search UI");
            return;
        }
        
        foreach (var zone in detectionZone.detectionZones)
        {
            GameObject searchUIObj;
            
            if (searchUIPrefab != null)
            {
                searchUIObj = Instantiate(searchUIPrefab, uiCanvas.transform);
            }
            else
            {
                searchUIObj = CreateDefaultSearchUI(zone.zoneName);
            }
            
            ZoneSearchUI searchUI = searchUIObj.GetComponent<ZoneSearchUI>();
            if (searchUI == null)
                searchUI = searchUIObj.AddComponent<ZoneSearchUI>();
            
            // Set references
            searchUI.database = database;
            searchUI.panelManager = this;
            searchUI.SetZoneName(zone.zoneName);
            zoneSearchUIs[zone.zoneName] = searchUI;
            
            Debug.Log($"Created search UI for zone: {zone.zoneName}, zone name set to: {searchUI.zoneName}");
            
            // Position search UI using anchors based on zone position
            RectTransform rect = searchUIObj.GetComponent<RectTransform>();
            if (rect != null)
            {
                // Set anchors to match zone center
                float anchorX = zone.x + zone.width * 0.5f;
                float anchorY = zone.y + zone.height * 0.5f;
                
                rect.anchorMin = new Vector2(anchorX, anchorY);
                rect.anchorMax = new Vector2(anchorX, anchorY);
                rect.anchoredPosition = searchUIOffset;
            }
            
            Debug.Log($"Created search UI for zone: {zone.zoneName}");

            // Hide the left zone's search UI by default if hideLeftPanelByDefault is enabled
            if (hideLeftPanelByDefault && zone.zoneName == leftZoneName)
            {
                searchUIObj.SetActive(false);
                Debug.Log($"Left zone search UI '{leftZoneName}' hidden by default");
            }
        }
    }

    void InitializeContextualButton()
    {
        if (!enableContextualButton) return;

        if (contextualButton == null)
        {
            // Create contextual button if not assigned
            GameObject buttonObj = new GameObject("ContextualLeftPanelButton");
            buttonObj.transform.SetParent(uiCanvas.transform, false);
            contextualButton = buttonObj.AddComponent<ContextualLeftPanelButton>();

            // Configure the button
            contextualButton.panelManager = this;
            contextualButton.uiCanvas = uiCanvas;
            contextualButton.showDebugInfo = enableDebugLogging;
        }
        else
        {
            // Configure existing button
            contextualButton.panelManager = this;
            if (contextualButton.uiCanvas == null)
                contextualButton.uiCanvas = uiCanvas;
        }

        Debug.Log("Contextual left panel button initialized");
    }
    
    GameObject CreateDefaultSearchUI(string zoneName)
    {
        GameObject searchUIObj = new GameObject($"SearchUI_{zoneName}");
        searchUIObj.transform.SetParent(uiCanvas.transform, false);
        
        RectTransform rect = searchUIObj.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(200, 40);
        
        // Create search button
        GameObject buttonObj = new GameObject("SearchButton");
        buttonObj.transform.SetParent(searchUIObj.transform, false);
        
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(200, 40);
        buttonRect.anchoredPosition = Vector2.zero;
        
        UnityEngine.UI.Image buttonImage = buttonObj.AddComponent<UnityEngine.UI.Image>();
        buttonImage.color = new Color(0.2f, 0.6f, 1f, 0.8f);
        
        UnityEngine.UI.Button button = buttonObj.AddComponent<UnityEngine.UI.Button>();
        
        // Add button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        TMPro.TextMeshProUGUI text = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        text.text = "Search Products";
        text.fontSize = 14;
        text.color = Color.white;
        text.alignment = TMPro.TextAlignmentOptions.Center;
        
        return searchUIObj;
    }
    
    void CreateDefaultPanel()
    {
        foreach (var zone in detectionZone.detectionZones)
        {
            GameObject panelObj = new GameObject($"Panel_{zone.zoneName}");
            panelObj.transform.SetParent(uiCanvas.transform, false);
            
            RectTransform rect = panelObj.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(300, 200);
            
            UnityEngine.UI.Image bg = panelObj.AddComponent<UnityEngine.UI.Image>();
            bg.color = new Color(0, 0, 0, 0.8f);
            
            VuMarkDataPanel panel = panelObj.AddComponent<VuMarkDataPanel>();
            zonePanels[zone.zoneName] = panel;
            
            Vector2 zoneCenter = GetZoneCenterPosition(zone);
            panel.SetPosition(zoneCenter + panelOffset);
            
            panel.Hide();
            
            Debug.Log($"Created default panel for zone: {zone.zoneName}");
        }
    }
    
    void HideAllPanels()
    {
        foreach (var panel in zonePanels.Values)
        {
            panel.Hide();
        }
        zoneVuMarkData.Clear();
    }
    
    void ClearVuMarkFromOtherZones(string vuMarkId, string currentZone)
    {
        var zonesToClear = new List<string>();
        
        foreach (var kvp in zoneVuMarkData)
        {
            if (kvp.Value == vuMarkId && kvp.Key != currentZone)
            {
                zonesToClear.Add(kvp.Key);
            }
        }
        
        foreach (string zone in zonesToClear)
        {
            zoneVuMarkData.Remove(zone);
            if (zonePanels.ContainsKey(zone))
            {
                zonePanels[zone].Hide();
            }
        }
    }
    
    Vector2 GetZoneCenterPosition(VuforiaDetectionZone.DetectionZone zone)
    {
        if (detectionZone.arCamera == null) return Vector2.zero;
        
        // Calculate bottom center position for info panels
        float centerX = (zone.x + zone.width * 0.5f) * Screen.width;
        float bottomY = zone.y * Screen.height; // Bottom of zone instead of center
        
        return new Vector2(centerX, bottomY);
    }
    
    Vector2 GetZoneSearchUIPosition(VuforiaDetectionZone.DetectionZone zone)
    {
        // Use same calculation as panels but return Vector2.zero for now
        // Search UI will be positioned using anchors instead
        return Vector2.zero;
    }
    
    void SubscribeToVuMarkEvents()
    {
        var vuMarkBehaviours = FindObjectsOfType<VuMarkBehaviour>();
        foreach (var vuMark in vuMarkBehaviours)
        {
            var eventHandler = vuMark.GetComponent<DefaultObserverEventHandler>();
            if (eventHandler != null)
            {
                eventHandler.OnTargetFound.AddListener(() => OnVuMarkDetected(vuMark));
                eventHandler.OnTargetLost.AddListener(() => OnVuMarkLost(vuMark));
            }
        }
    }
    
    void OnVuMarkDetected(VuMarkBehaviour vuMark)
    {
        string vuMarkId = vuMark.InstanceId?.ToString() ?? vuMark.TargetName;
        
        // Wait a frame to ensure VuMark position is stable
        StartCoroutine(ProcessVuMarkDetection(vuMark, vuMarkId));
    }
    
    System.Collections.IEnumerator ProcessVuMarkDetection(VuMarkBehaviour vuMark, string vuMarkId)
    {
        yield return null; // Wait one frame
        
        if (vuMark == null || !vuMark.gameObject.activeInHierarchy) yield break;
        
        string zoneName = GetVuMarkZone(vuMark);
        
        Debug.Log($"VuMark detected: {vuMarkId}, Zone: {zoneName}");
        
        if (!string.IsNullOrEmpty(zoneName) && zonePanels.ContainsKey(zoneName))
        {
            // Clear any existing data for this VuMark in other zones
            ClearVuMarkFromOtherZones(vuMarkId, zoneName);
            
            // Set data for correct zone
            zoneVuMarkData[zoneName] = vuMarkId;
            RequestAndShowData(zoneName, vuMarkId);

            // Trigger VuMark detection animations
            if (animationController != null)
            {
                animationController.TriggerVuMarkDetectedAnimations(zoneName);
            }

            // Refresh contextual button visibility
            RefreshContextualButton();
        }
        else
        {
            Debug.Log($"VuMark {vuMarkId} is outside detection zones");
        }
    }
    
    void OnVuMarkLost(VuMarkBehaviour vuMark)
    {
        string vuMarkId = vuMark.InstanceId?.ToString() ?? vuMark.TargetName;
        
        Debug.Log($"VuMark lost: {vuMarkId}");
        
        // Find and hide panel for this specific VuMark (but keep search data)
        foreach (var kvp in zoneVuMarkData.ToArray())
        {
            if (kvp.Value == vuMarkId)
            {
                if (zonePanels.ContainsKey(kvp.Key) && !zoneSearchData.ContainsKey(kvp.Key))
                {
                    zonePanels[kvp.Key].Hide();
                    Debug.Log($"Hiding panel for zone: {kvp.Key}");
                }

                // Trigger VuMark lost animations
                if (animationController != null)
                {
                    animationController.TriggerVuMarkLostAnimations(kvp.Key);
                }

                zoneVuMarkData.Remove(kvp.Key);
                break;
            }
        }
    }
    
    public void ShowProductInZone(string zoneName, ProductData product)
    {
        Debug.Log($"ShowProductInZone called with zone: {zoneName}, product: {product?.Name}");
        Debug.Log($"Available zones: {string.Join(", ", zonePanels.Keys)}");
        
        if (!zonePanels.ContainsKey(zoneName))
        {
            Debug.LogError($"No panel found for zone: {zoneName}. Available zones: {string.Join(", ", zonePanels.Keys)}");
            return;
        }
        
        if (product == null)
        {
            Debug.LogError("Product is null!");
            return;
        }
        
        // Store search data
        zoneSearchData[zoneName] = product;
        
        // Clear VuMark data for this zone since we're showing search result
        if (zoneVuMarkData.ContainsKey(zoneName))
        {
            zoneVuMarkData.Remove(zoneName);
        }
        
        // Show the product data
        var panel = zonePanels[zoneName];
        if (panel == null)
        {
            Debug.LogError($"Panel for zone {zoneName} is null!");
            return;
        }
        
        Debug.Log($"Calling ShowData on panel for zone {zoneName}");
        panel.ShowData(product);

        Debug.Log($"Showing search result in zone {zoneName}: {product.Name}");

        // Trigger search results animations
        if (animationController != null)
        {
            animationController.TriggerSearchResultsAnimations(zoneName);
        }

        // Refresh contextual button visibility
        RefreshContextualButton();
    }
    
    public void ClearSearchDataForZone(string zoneName)
    {
        if (zoneSearchData.ContainsKey(zoneName))
        {
            zoneSearchData.Remove(zoneName);
            
            // Hide panel if no VuMark data either
            if (!zoneVuMarkData.ContainsKey(zoneName) && zonePanels.ContainsKey(zoneName))
            {
                zonePanels[zoneName].Hide();
            }

            // Refresh contextual button visibility
            RefreshContextualButton();
        }
    }
    
    string GetVuMarkZone(VuMarkBehaviour vuMark)
    {
        if (detectionZone == null || detectionZone.arCamera == null || vuMark == null) return null;
        
        Vector3 worldPos = vuMark.transform.position;
        Vector3 screenPos = detectionZone.arCamera.WorldToScreenPoint(worldPos);
        
        // Check if VuMark is in front of camera
        if (screenPos.z <= 0) return null;
        
       // Debug.Log($"VuMark screen position: {screenPos.x}, {screenPos.y}");
        
        // Check zones in order (left first, then right)
        var sortedZones = detectionZone.detectionZones.OrderBy(z => z.x).ToList();
        
        foreach (var zone in sortedZones)
        {
            if (zone.isActive && zone.ContainsScreenPoint(new Vector2(screenPos.x, screenPos.y), detectionZone.arCamera))
            {
              //  Debug.Log($"VuMark is in zone: {zone.zoneName} at screen pos ({screenPos.x:F1}, {screenPos.y:F1})");
              //  Debug.Log($"Zone bounds: x={zone.x * Screen.width:F1}, y={zone.y * Screen.height:F1}, w={zone.width * Screen.width:F1}, h={zone.height * Screen.height:F1}");
                return zone.zoneName;
            }
        }
       //Debug pos 
       // Debug.Log($"VuMark at ({screenPos.x:F1}, {screenPos.y:F1}) is not in any active zone");
        return null;
    }
    
    void RequestAndShowData(string zoneName, string vuMarkId)
    {
        Debug.Log($"Requesting data for VuMark: {vuMarkId} in zone: {zoneName}");
        
        if (database == null)
        {
            Debug.LogError("Database is null");
            return;
        }
        
        if (!database.IsDatabaseReady)
        {
            Debug.LogError("Database is not ready");
            return;
        }
        
        var productData = database.GetProductDataByName(vuMarkId);
       // Debug.Log($"Found {productData.Count} products for {vuMarkId}");
        
        if (productData.Count > 0 && zonePanels.ContainsKey(zoneName))
        {
            //Debug.Log($"Showing data for {productData[0].Name}");
            zonePanels[zoneName].ShowData(productData[0]);
        }
    }
    
    [UnityEngine.ContextMenu("Force Show Test Panel")]
    public void ForceShowTestPanel()
    {
        if (zonePanels.Count == 0)
        {
            Debug.LogError("No panels created!");
            return;
        }
        
        var testData = new ProductData { Name = "FORCE TEST", Price = 999, Info = "This is a forced test panel" };
        
        foreach (var kvp in zonePanels)
        {
            Debug.Log($"Force showing panel for zone: {kvp.Key}");
            kvp.Value.ShowData(testData);
        }
    }
    
    [UnityEngine.ContextMenu("Toggle Search Mode for All Zones")]
    public void ToggleSearchModeForAllZones()
    {
        foreach (var kvp in zoneSearchUIs)
        {
            kvp.Value.ToggleSearchMode();
        }
    }
    
    public void RefreshPanelVisibility()
    {
        foreach (var kvp in zoneVuMarkData)
        {
            if (zonePanels.ContainsKey(kvp.Key))
            {
                RequestAndShowData(kvp.Key, kvp.Value);
            }
        }
    }
    
    public void ToggleZoneMode(string zoneName)
    {
        if (zoneSearchUIs.ContainsKey(zoneName))
        {
            var searchUI = zoneSearchUIs[zoneName];
            searchUI.ToggleSearchMode();
        }
    }
    
    public void SetZoneSearchMode(string zoneName, bool searchMode)
    {
        if (zoneSearchUIs.ContainsKey(zoneName))
        {
            var searchUI = zoneSearchUIs[zoneName];
            if (searchMode)
            {
                searchUI.ToggleSearchMode();
            }
            else
            {
                // Clear search data and return to VuMark mode
                ClearSearchDataForZone(zoneName);
            }
        }
    }

    // Public methods for ContextualLeftPanelButton integration
    public bool HasContentInZone(string zoneName)
    {
        return (zoneVuMarkData.ContainsKey(zoneName) && !string.IsNullOrEmpty(zoneVuMarkData[zoneName])) ||
               (zoneSearchData.ContainsKey(zoneName) && zoneSearchData[zoneName] != null);
    }

    public bool HasAnyContent()
    {
        return zoneVuMarkData.Count > 0 || zoneSearchData.Count > 0;
    }

    public bool HasContentInNonLeftZones(string leftZoneName)
    {
        foreach (var kvp in zoneVuMarkData)
        {
            if (kvp.Key != leftZoneName && !string.IsNullOrEmpty(kvp.Value))
                return true;
        }

        foreach (var kvp in zoneSearchData)
        {
            if (kvp.Key != leftZoneName && kvp.Value != null)
                return true;
        }

        return false;
    }

    public bool HasContentInRightPanels()
    {
        bool hasContent = HasContentInNonLeftZones(leftZoneName);

        if (enableDebugLogging)
        {
            Debug.Log($"DetectionZonePanelManager: HasContentInRightPanels() = {hasContent}, leftZoneName = '{leftZoneName}'");
            Debug.Log($"DetectionZonePanelManager: zoneVuMarkData count = {zoneVuMarkData.Count}, zoneSearchData count = {zoneSearchData.Count}");

            foreach (var kvp in zoneVuMarkData)
            {
                Debug.Log($"DetectionZonePanelManager: VuMark data - Zone: '{kvp.Key}', Data: '{kvp.Value}', IsLeftZone: {kvp.Key == leftZoneName}");
            }

            foreach (var kvp in zoneSearchData)
            {
                Debug.Log($"DetectionZonePanelManager: Search data - Zone: '{kvp.Key}', HasData: {kvp.Value != null}, IsLeftZone: {kvp.Key == leftZoneName}");
            }
        }

        return hasContent;
    }

    public string GetLeftmostZoneName()
    {
        if (detectionZone?.detectionZones == null) return null;

        var leftmostZone = detectionZone.detectionZones
            .Where(z => z.isActive)
            .OrderBy(z => z.x)
            .FirstOrDefault();

        return leftmostZone?.zoneName;
    }

    public void ShowAnyContentInZone(string targetZoneName)
    {
        // Try to show search data first
        if (zoneSearchData.Count > 0)
        {
            var firstSearchData = zoneSearchData.First();
            ShowProductInZone(targetZoneName, firstSearchData.Value);
            return;
        }

        // Then try VuMark data
        if (zoneVuMarkData.Count > 0)
        {
            var firstVuMarkData = zoneVuMarkData.First();
            RequestAndShowData(targetZoneName, firstVuMarkData.Value);
            return;
        }
    }

    // Make RequestAndShowData public for external access
    public void RequestAndShowDataPublic(string zoneName, string vuMarkId)
    {
        RequestAndShowData(zoneName, vuMarkId);
    }

    void RefreshContextualButton()
    {
        if (contextualButton != null)
        {
            contextualButton.RefreshButtonVisibility();
        }
    }

    // Public method to manually refresh the contextual button
    public void RefreshContextualButtonPublic()
    {
        RefreshContextualButton();
    }

    // Methods for managing left panel visibility
    public void ShowLeftPanel()
    {
        if (string.IsNullOrEmpty(leftZoneName)) return;

        // Enable the left detection zone
        if (detectionZone != null)
        {
            detectionZone.EnableLeftDetectionZone();
        }

        // Show the left panel GameObject
        if (zonePanels.ContainsKey(leftZoneName))
        {
            zonePanels[leftZoneName].gameObject.SetActive(true);
            leftPanelVisible = true;
            Debug.Log($"Left panel '{leftZoneName}' shown");
        }

        // Show the left zone's search UI
        if (zoneSearchUIs.ContainsKey(leftZoneName))
        {
            zoneSearchUIs[leftZoneName].gameObject.SetActive(true);
            Debug.Log($"Left zone search UI '{leftZoneName}' shown");
        }

        // Trigger left panel show animations
        if (animationController != null)
        {
            animationController.TriggerLeftPanelAnimations(true);
        }
    }

    public void HideLeftPanel()
    {
        if (string.IsNullOrEmpty(leftZoneName)) return;

        // Disable the left detection zone
        if (detectionZone != null)
        {
            detectionZone.DisableLeftDetectionZone();
        }

        // Clear any existing data for the left zone
        ClearLeftZoneData();

        // Hide the left panel GameObject
        if (zonePanels.ContainsKey(leftZoneName))
        {
            zonePanels[leftZoneName].gameObject.SetActive(false);
            leftPanelVisible = false;
            Debug.Log($"Left panel '{leftZoneName}' hidden");
        }

        // Hide the left zone's search UI
        if (zoneSearchUIs.ContainsKey(leftZoneName))
        {
            zoneSearchUIs[leftZoneName].gameObject.SetActive(false);
            Debug.Log($"Left zone search UI '{leftZoneName}' hidden");
        }

        // Trigger left panel hide animations
        if (animationController != null)
        {
            animationController.TriggerLeftPanelAnimations(false);
        }
    }

    void ClearLeftZoneData()
    {
        if (string.IsNullOrEmpty(leftZoneName)) return;

        // Clear VuMark data for left zone
        if (zoneVuMarkData.ContainsKey(leftZoneName))
        {
            zoneVuMarkData[leftZoneName] = "";
        }

        // Clear search data for left zone
        if (zoneSearchData.ContainsKey(leftZoneName))
        {
            zoneSearchData[leftZoneName] = null;
        }

        // Hide the panel content
        if (zonePanels.ContainsKey(leftZoneName))
        {
            zonePanels[leftZoneName].Hide();
        }

        Debug.Log($"Cleared data for left zone '{leftZoneName}'");
    }

    public void ToggleLeftPanel()
    {
        if (leftPanelVisible)
        {
            HideLeftPanel();
        }
        else
        {
            ShowLeftPanel();
        }
    }

    public bool IsLeftPanelVisible()
    {
        // Check both the panel state and the detection zone state
        bool detectionZoneActive = detectionZone != null && detectionZone.IsLeftDetectionZoneActive();
        return leftPanelVisible && detectionZoneActive;
    }

    public string GetLeftZoneName()
    {
        return leftZoneName;
    }

    // Public getters for contextual button to access zone data
    public bool HasSearchDataInZone(string zoneName)
    {
        return zoneSearchData.ContainsKey(zoneName) && zoneSearchData[zoneName] != null;
    }

    public bool HasVuMarkDataInZone(string zoneName)
    {
        return zoneVuMarkData.ContainsKey(zoneName) && !string.IsNullOrEmpty(zoneVuMarkData[zoneName]);
    }

    // Public method for testing - clears all panel data
    public void ClearAllPanelDataForTesting()
    {
        HideAllPanelsAndClearData();
    }
}