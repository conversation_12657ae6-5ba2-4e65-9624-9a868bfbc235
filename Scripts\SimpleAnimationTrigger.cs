using DhafinFawwaz.AnimationUILib;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// Alternative simple approach for triggering animations.
/// Use this if you prefer a more direct, UnityEvent-based approach.
/// </summary>
public class SimpleAnimationTrigger : MonoBehaviour
{
    [Header("Animation References")]
    [Toolt<PERSON>("Animations to play forward")]
    public AnimationUI[] animationsToPlay;
    
    [<PERSON>lt<PERSON>("Animations to play in reverse")]
    public AnimationUI[] animationsToReverse;
    
    [Header("Timing")]
    public float delayBeforePlay = 0f;
    public float delayBeforeReverse = 0f;
    
    [Header("Unity Events")]
    [Tooltip("Called when animations start playing")]
    public UnityEvent OnAnimationsStarted;
    
    [Toolt<PERSON>("Called when reverse animations start playing")]
    public UnityEvent OnReverseAnimationsStarted;
    
    [Header("Debug")]
    public bool enableDebugLogging = false;
    
    /// <summary>
    /// Play all forward animations
    /// </summary>
    public void PlayAnimations()
    {
        if (enableDebugLogging)
            Debug.Log($"SimpleAnimationTrigger: Playing {animationsToPlay.Length} forward animations");
            
        StartCoroutine(PlayAnimationsWithDelay(animationsToPlay, delayBeforePlay, true));
    }
    
    /// <summary>
    /// Play all reverse animations
    /// </summary>
    public void ReverseAnimations()
    {
        if (enableDebugLogging)
            Debug.Log($"SimpleAnimationTrigger: Playing {animationsToReverse.Length} reverse animations");
            
        StartCoroutine(PlayAnimationsWithDelay(animationsToReverse, delayBeforeReverse, false));
    }
    
    /// <summary>
    /// Play specific animation forward
    /// </summary>
    public void PlayAnimation(AnimationUI animation)
    {
        if (animation != null)
        {
            if (enableDebugLogging)
                Debug.Log($"SimpleAnimationTrigger: Playing single animation '{animation.name}' forward");
                
            animation.Play();
        }
    }
    
    /// <summary>
    /// Play specific animation in reverse
    /// </summary>
    public void ReverseAnimation(AnimationUI animation)
    {
        if (animation != null)
        {
            if (enableDebugLogging)
                Debug.Log($"SimpleAnimationTrigger: Playing single animation '{animation.name}' in reverse");
                
            animation.PlayReversed();
        }
    }
    
    /// <summary>
    /// Toggle between forward and reverse animations
    /// </summary>
    public void ToggleAnimations()
    {
        // Simple toggle logic - you can customize this
        if (Time.time % 2 < 1)
        {
            PlayAnimations();
        }
        else
        {
            ReverseAnimations();
        }
    }
    
    private System.Collections.IEnumerator PlayAnimationsWithDelay(AnimationUI[] animations, float delay, bool forward)
    {
        if (delay > 0)
        {
            yield return new WaitForSeconds(delay);
        }
        
        if (forward)
        {
            OnAnimationsStarted?.Invoke();
        }
        else
        {
            OnReverseAnimationsStarted?.Invoke();
        }
        
        foreach (var animation in animations)
        {
            if (animation != null)
            {
                if (forward)
                {
                    animation.Play();
                }
                else
                {
                    animation.PlayReversed();
                }
            }
        }
    }
    
    #region Context Menu Helpers
    
    [ContextMenu("Test Play Animations")]
    public void TestPlayAnimations()
    {
        PlayAnimations();
    }
    
    [ContextMenu("Test Reverse Animations")]
    public void TestReverseAnimations()
    {
        ReverseAnimations();
    }
    
    [ContextMenu("Find All AnimationUI in Scene")]
    public void FindAllAnimationUI()
    {
        var allAnimations = FindObjectsOfType<AnimationUI>();
        Debug.Log($"Found {allAnimations.Length} AnimationUI objects in scene:");
        foreach (var anim in allAnimations)
        {
            Debug.Log($"  - {anim.name}");
        }
    }
    
    #endregion
}
