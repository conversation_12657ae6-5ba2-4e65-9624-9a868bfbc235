%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &39153219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 39153220}
  - component: {fileID: 39153222}
  - component: {fileID: 39153221}
  m_Layer: 5
  m_Name: White
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &39153220
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 39153219}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1788952090}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &39153221
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 39153219}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: af1a0da324ed10c47b60c8e9945a55ef, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 0
  m_FillClockwise: 1
  m_FillOrigin: 1
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &39153222
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 39153219}
  m_CullTransparentMesh: 1
--- !u!1 &79688088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 79688089}
  - component: {fileID: 79688091}
  - component: {fileID: 79688090}
  m_Layer: 5
  m_Name: Left
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &79688089
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 79688088}
  m_LocalRotation: {x: 0, y: 0, z: 0.08715578, w: 0.9961947}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 292233844}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 10}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 1500}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &79688090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 79688088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.11764706, g: 0.14509805, b: 0.19607843, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &79688091
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 79688088}
  m_CullTransparentMesh: 1
--- !u!1 &87242022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 87242023}
  - component: {fileID: 87242025}
  - component: {fileID: 87242024}
  - component: {fileID: 87242026}
  m_Layer: 5
  m_Name: Box (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &87242023
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 87242022}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1304071251}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -570, y: 257}
  m_SizeDelta: {x: 134.8617, y: 134.86}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &87242024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 87242022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.8509804, b: 0.4, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &87242025
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 87242022}
  m_CullTransparentMesh: 1
--- !u!114 &87242026
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 87242022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_HighlightedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_PressedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_SelectedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_DisabledColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 87242024}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &206877567
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 206877568}
  - component: {fileID: 206877570}
  - component: {fileID: 206877569}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &206877568
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 206877567}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1590290164}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &206877569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 206877567}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Home
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_sharedMaterial: {fileID: -3388997479985545262, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &206877570
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 206877567}
  m_CullTransparentMesh: 1
--- !u!1 &221682839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221682840}
  - component: {fileID: 221682841}
  m_Layer: 0
  m_Name: Start -> Lose
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221682840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221682839}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1713836260}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &221682841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221682839}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f54930f0f8f7f2478377a55d6c33e4c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TotalDuration: 0.61
  AnimationSequence:
  - AtTime: At 0s [Wait 0.01s]
    StartTime: 0
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 133
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.01
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [SetActiveAllInput to False]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 155
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [CanvasLose] [SetActive to True]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 177
    SequenceType: 3
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 1054948131}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [Wait 0.3s]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 199
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.31s [CanvasLose] [CanvasGroup]
    StartTime: 0.31
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 221
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1054948136}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.31s [Blocker] [RectTransform]
    StartTime: 0.31
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 243
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 1748108035}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 2
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 1.5, y: 1.5, z: 1.5}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.31s [1] [SFX]
    StartTime: 0.31
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 265
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.31s [Wait 0.3s]
    StartTime: 0.31
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 287
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.61s [SetActiveAllInput to True]
    StartTime: 0.61
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 309
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  PlayOnStart: 0
  CurrentTime: 0
  IsPlayingInEditMode: 0
--- !u!1 &292233840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 292233844}
  - component: {fileID: 292233843}
  - component: {fileID: 292233842}
  - component: {fileID: 292233841}
  m_Layer: 5
  m_Name: Home
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &292233841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 292233840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &292233842
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 292233840}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 1
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &292233843
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 292233840}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &292233844
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 292233840}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 79688089}
  - {fileID: 738017527}
  - {fileID: 1407296032}
  - {fileID: 507370362}
  - {fileID: 1304071251}
  - {fileID: 1150971455}
  - {fileID: 1788952090}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &307511742
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 307511743}
  - component: {fileID: 307511745}
  - component: {fileID: 307511744}
  - component: {fileID: 307511746}
  m_Layer: 5
  m_Name: Box (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &307511743
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 307511742}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1150971455}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -317, y: -138.00003}
  m_SizeDelta: {x: 105.7079, y: 105.7079}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &307511744
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 307511742}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &307511745
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 307511742}
  m_CullTransparentMesh: 1
--- !u!114 &307511746
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 307511742}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_HighlightedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_PressedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_SelectedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_DisabledColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 307511744}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &487557862
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 487557863}
  - component: {fileID: 487557865}
  - component: {fileID: 487557864}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &487557863
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 487557862}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 908745315}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &487557864
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 487557862}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Win
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_sharedMaterial: {fileID: -3388997479985545262, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4283909184
  m_fontColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &487557865
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 487557862}
  m_CullTransparentMesh: 1
--- !u!1 &507370361
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 507370362}
  - component: {fileID: 507370365}
  - component: {fileID: 507370364}
  - component: {fileID: 507370363}
  m_Layer: 5
  m_Name: Next
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &507370362
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507370361}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1196117272}
  m_Father: {fileID: 292233844}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: 141.00003, y: 125}
  m_SizeDelta: {x: 250, y: 89}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &507370363
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507370361}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_HighlightedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_PressedColor: {r: 1, g: 1, b: 1, a: 1}
    m_SelectedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 507370364}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 1196117273}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1150971454}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 1304071250}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &507370364
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507370361}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c566cda3cafcb594397d8ad8306e4916, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &507370365
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507370361}
  m_CullTransparentMesh: 1
--- !u!1 &680096085
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 680096086}
  - component: {fileID: 680096088}
  - component: {fileID: 680096087}
  - component: {fileID: 680096089}
  m_Layer: 5
  m_Name: Box (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &680096086
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 680096085}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1304071251}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -3.1951904, y: 286.5235}
  m_SizeDelta: {x: 200, y: 200}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &680096087
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 680096085}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &680096088
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 680096085}
  m_CullTransparentMesh: 1
--- !u!114 &680096089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 680096085}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_HighlightedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_PressedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_SelectedColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_DisabledColor: {r: 0.9490196, g: 0.43529412, b: 0.40392157, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 680096087}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &738017526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 738017527}
  - component: {fileID: 738017529}
  - component: {fileID: 738017528}
  m_Layer: 5
  m_Name: Right
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &738017527
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 738017526}
  m_LocalRotation: {x: 0, y: 0, z: 0.08715578, w: 0.9961947}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 292233844}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 10}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 200, y: 1500}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &738017528
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 738017526}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.11764706, g: 0.14509805, b: 0.19607843, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &738017529
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 738017526}
  m_CullTransparentMesh: 1
--- !u!1 &821842490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 821842491}
  - component: {fileID: 821842492}
  m_Layer: 0
  m_Name: Home -> Start
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &821842491
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821842490}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1713836260}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &821842492
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821842490}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f54930f0f8f7f2478377a55d6c33e4c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TotalDuration: 2.2599998
  AnimationSequence:
  - AtTime: At 0s [Wait 0.01s]
    StartTime: 0
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 133
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.01
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [SetActiveAllInput to False]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 155
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [Wait 0.5s]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 177
    SequenceType: 1
    EaseType: 0
    EasePower: 0
    TargetType: 3
    TargetComp: {fileID: 1232869273}
    Duration: 0.5
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 0
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 1, g: 1, b: 1, a: 0}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.51s [White] [Image]
    StartTime: 0.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 199
    SequenceType: 0
    EaseType: 0
    EasePower: 0
    TargetType: 3
    TargetComp: {fileID: 39153220}
    Duration: 0.2
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 0
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.51s [Wait 1s]
    StartTime: 0.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 221
    SequenceType: 1
    EaseType: 1
    EasePower: 0
    TargetType: 1
    TargetComp: {fileID: 1630439081}
    Duration: 1
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 2
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.51s [Box] [RectTransform]
    StartTime: 1.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 243
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 1630439081}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: -0, y: 0, z: 180}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.51s [1] [SFX]
    StartTime: 1.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 265
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.51s [Wait 0.05s]
    StartTime: 1.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 287
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.05
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.56s [Box (1)] [RectTransform]
    StartTime: 1.56
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 309
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 87242023}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: -0, y: 0, z: 180}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.56s [Wait 0.05s]
    StartTime: 1.56
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 331
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.05
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.61s [1] [SFX]
    StartTime: 1.6099999
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 353
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.61s [Box (2)] [RectTransform]
    StartTime: 1.6099999
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 375
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 680096086}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: -0, y: 0, z: 180}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.61s [Wait 0.05s]
    StartTime: 1.6099999
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 397
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.05
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.66s [1] [SFX]
    StartTime: 1.6599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 419
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.66s [Box (3)] [RectTransform]
    StartTime: 1.6599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 441
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 1359980274}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: -0, y: 0, z: 180}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.66s [Wait 0.05s]
    StartTime: 1.6599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 463
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.05
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.71s [1] [SFX]
    StartTime: 1.7099998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 485
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.71s [Box (4)] [RectTransform]
    StartTime: 1.7099998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 507
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: -0, y: 0, z: 180}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.71s [Wait 0.05s]
    StartTime: 1.7099998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 529
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.05
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.76s [1] [SFX]
    StartTime: 1.7599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 551
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.76s [Next] [RectTransform]
    StartTime: 1.7599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 573
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 507370362}
    Duration: 0.2
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 2
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.76s [Prev] [RectTransform]
    StartTime: 1.7599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 595
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 1407296032}
    Duration: 0.2
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 2
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 1.76s [Wait 0.5s]
    StartTime: 1.7599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 617
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.5
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 2.26s [SetActiveAllInput to True]
    StartTime: 2.2599998
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 639
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  PlayOnStart: 1
  CurrentTime: 0
  IsPlayingInEditMode: 0
--- !u!1 &908745314
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 908745315}
  - component: {fileID: 908745317}
  - component: {fileID: 908745316}
  - component: {fileID: 908745318}
  m_Layer: 5
  m_Name: Box (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &908745315
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908745314}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 487557863}
  m_Father: {fileID: 1304071251}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 237.23914, y: -151.52808}
  m_SizeDelta: {x: 150.6873, y: 150.6873}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &908745316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908745314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &908745317
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908745314}
  m_CullTransparentMesh: 1
--- !u!114 &908745318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 908745314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_HighlightedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_PressedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_SelectedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_DisabledColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 908745316}
  _textColors:
    m_NormalColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_HighlightedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_PressedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_SelectedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_DisabledColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 487557864}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1421636218}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &945082780
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 945082781}
  - component: {fileID: 945082783}
  - component: {fileID: 945082782}
  - component: {fileID: 945082784}
  m_Layer: 5
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &945082781
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 945082780}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1150971455}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -317, y: 303}
  m_SizeDelta: {x: 157.922, y: 157.9189}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &945082782
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 945082780}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &945082783
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 945082780}
  m_CullTransparentMesh: 1
--- !u!114 &945082784
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 945082780}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_HighlightedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_PressedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_SelectedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_DisabledColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 945082782}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1033884009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1033884012}
  - component: {fileID: 1033884011}
  - component: {fileID: 1033884010}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1033884010
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033884009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1033884011
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033884009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1033884012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033884009}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1054948131
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1054948136}
  - component: {fileID: 1054948135}
  - component: {fileID: 1054948134}
  - component: {fileID: 1054948133}
  - component: {fileID: 1054948132}
  m_Layer: 5
  m_Name: CanvasLose
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!225 &1054948132
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054948131}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1054948133
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054948131}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1054948134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054948131}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 1
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1054948135
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054948131}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 10
  m_TargetDisplay: 0
--- !u!224 &1054948136
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054948131}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1748108035}
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1067189692
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1067189695}
  - component: {fileID: 1067189694}
  - component: {fileID: 1067189693}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1067189693
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1067189692}
  m_Enabled: 1
--- !u!20 &1067189694
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1067189692}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.16862746, g: 0.18431373, b: 0.24313726, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1067189695
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1067189692}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1150971454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1150971455}
  m_Layer: 5
  m_Name: 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1150971455
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150971454}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 945082781}
  - {fileID: 1479102576}
  - {fileID: 307511743}
  - {fileID: 1684212275}
  - {fileID: 1348865906}
  m_Father: {fileID: 292233844}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1196117271
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1196117272}
  - component: {fileID: 1196117274}
  - component: {fileID: 1196117273}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1196117272
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196117271}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 507370362}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1196117273
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196117271}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Next
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_sharedMaterial: {fileID: -3388997479985545262, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1196117274
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1196117271}
  m_CullTransparentMesh: 1
--- !u!1 &1220719854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1220719855}
  - component: {fileID: 1220719857}
  - component: {fileID: 1220719856}
  m_Layer: 5
  m_Name: WinBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1220719855
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1220719854}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1512884867}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 732, y: 734}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1220719856
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1220719854}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 0aa255ac2cf2d4f45bcebbfaee94da89, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1220719857
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1220719854}
  m_CullTransparentMesh: 1
--- !u!1 &1232869272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1232869273}
  - component: {fileID: 1232869275}
  - component: {fileID: 1232869274}
  m_Layer: 5
  m_Name: White
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1232869273
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1232869272}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1512884867}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1232869274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1232869272}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: af1a0da324ed10c47b60c8e9945a55ef, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 0
  m_FillClockwise: 1
  m_FillOrigin: 1
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1232869275
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1232869272}
  m_CullTransparentMesh: 1
--- !u!1 &1304071250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1304071251}
  m_Layer: 5
  m_Name: 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1304071251
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1304071250}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1630439081}
  - {fileID: 87242023}
  - {fileID: 680096086}
  - {fileID: 1359980274}
  - {fileID: 908745315}
  m_Father: {fileID: 292233844}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1348865905
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1348865906}
  - component: {fileID: 1348865909}
  - component: {fileID: 1348865908}
  - component: {fileID: 1348865910}
  m_Layer: 5
  m_Name: Box (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1348865906
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348865905}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1150971455}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 499, y: 216}
  m_SizeDelta: {x: 42.247, y: 42.2474}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1348865908
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348865905}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1348865909
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348865905}
  m_CullTransparentMesh: 1
--- !u!114 &1348865910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348865905}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_HighlightedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_PressedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_SelectedColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_DisabledColor: {r: 0.5137255, g: 0.91764706, b: 0.5764706, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1348865908}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1359980273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1359980274}
  - component: {fileID: 1359980276}
  - component: {fileID: 1359980275}
  - component: {fileID: 1359980277}
  m_Layer: 5
  m_Name: Box (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1359980274
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1359980273}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1304071251}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -372.08072, y: -121.8855}
  m_SizeDelta: {x: 257.0385, y: 257.04}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1359980275
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1359980273}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1359980276
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1359980273}
  m_CullTransparentMesh: 1
--- !u!114 &1359980277
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1359980273}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_HighlightedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_PressedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_SelectedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_DisabledColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1359980275}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1407296031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1407296032}
  - component: {fileID: 1407296035}
  - component: {fileID: 1407296034}
  - component: {fileID: 1407296033}
  m_Layer: 5
  m_Name: Prev
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1407296032
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407296031}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1846112383}
  m_Father: {fileID: 292233844}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: -143, y: 125}
  m_SizeDelta: {x: 250, y: 89}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1407296033
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407296031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_HighlightedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_PressedColor: {r: 1, g: 1, b: 1, a: 1}
    m_SelectedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1407296034}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 1846112384}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1304071250}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 1150971454}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1407296034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407296031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.29411766, g: 0.31764707, b: 0.40392157, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c566cda3cafcb594397d8ad8306e4916, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1407296035
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407296031}
  m_CullTransparentMesh: 1
--- !u!1 &1421636216
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1421636217}
  - component: {fileID: 1421636218}
  m_Layer: 0
  m_Name: Start -> Win
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1421636217
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1421636216}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1713836260}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1421636218
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1421636216}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f54930f0f8f7f2478377a55d6c33e4c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TotalDuration: 0.51
  AnimationSequence:
  - AtTime: At 0s [Wait 0.01s]
    StartTime: 0
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 133
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.01
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [SetActiveAllInput to False]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 155
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [CanvasWin] [SetActive to True]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 177
    SequenceType: 3
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 1485837997}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [CanvasWin] [CanvasGroup]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 199
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [Blocker] [RectTransform]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 221
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 1512884867}
    Duration: 0.3
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 2
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 1.5, y: 1.5, z: 1.5}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [1] [SFX]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 243
    SequenceType: 4
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 1
    SFXFile: {fileID: 0}
    SFXIndex: 1
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [Wait 0.5s]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 265
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0.5
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 1
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.51s [SetActiveAllInput to True]
    StartTime: 0.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 287
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 4
    TargetComp: {fileID: 1485838001}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 1
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 1
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 0, g: 0, b: 0, a: 0}
    ColorEnd: {r: 0, g: 0, b: 0, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 1
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 1
    TargetCamTask: 1
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  PlayOnStart: 0
  CurrentTime: 0
  IsPlayingInEditMode: 0
--- !u!1 &1479102575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1479102576}
  - component: {fileID: 1479102578}
  - component: {fileID: 1479102577}
  - component: {fileID: 1479102579}
  m_Layer: 5
  m_Name: Box (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1479102576
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479102575}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1150971455}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 125, y: 175.29}
  m_SizeDelta: {x: 273.9438, y: 273.9403}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1479102577
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479102575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.8509804, b: 0.4, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1479102578
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479102575}
  m_CullTransparentMesh: 1
--- !u!114 &1479102579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479102575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_HighlightedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_PressedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_SelectedColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_DisabledColor: {r: 1, g: 0.8509804, b: 0.4, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1479102577}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1485837997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1485838001}
  - component: {fileID: 1485838000}
  - component: {fileID: 1485837999}
  - component: {fileID: 1485837998}
  - component: {fileID: 1485838002}
  m_Layer: 5
  m_Name: CanvasWin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &1485837998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485837997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1485837999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485837997}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 1
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1485838000
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485837997}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 10
  m_TargetDisplay: 0
--- !u!224 &1485838001
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485837997}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1512884867}
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!225 &1485838002
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485837997}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!1 &1493417135
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1493417136}
  - component: {fileID: 1493417139}
  - component: {fileID: 1493417138}
  - component: {fileID: 1493417137}
  m_Layer: 5
  m_Name: Next
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1493417136
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493417135}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1930784524}
  m_Father: {fileID: 1748108035}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: 0, y: 270}
  m_SizeDelta: {x: 250, y: 89}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1493417137
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493417135}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_HighlightedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_PressedColor: {r: 1, g: 1, b: 1, a: 1}
    m_SelectedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1493417138}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 1930784525}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1514292393}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1493417138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493417135}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c566cda3cafcb594397d8ad8306e4916, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1493417139
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493417135}
  m_CullTransparentMesh: 1
--- !u!1 &1512884866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1512884867}
  - component: {fileID: 1512884869}
  - component: {fileID: 1512884868}
  m_Layer: 5
  m_Name: Blocker
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1512884867
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1512884866}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1220719855}
  - {fileID: 1590290164}
  - {fileID: 1232869273}
  m_Father: {fileID: 1485838001}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1512884868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1512884866}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.4}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1512884869
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1512884866}
  m_CullTransparentMesh: 1
--- !u!1 &1514292391
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1514292392}
  - component: {fileID: 1514292393}
  m_Layer: 0
  m_Name: Start -> Home
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1514292392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514292391}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1713836260}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1514292393
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514292391}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f54930f0f8f7f2478377a55d6c33e4c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TotalDuration: 0.51
  AnimationSequence:
  - AtTime: At 0s [Wait 0.01s]
    StartTime: 0
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 133
    SequenceType: 1
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0.01
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 1
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [SetActiveAllInput to False]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 155
    SequenceType: 2
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [White] [Image]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 177
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 3
    TargetComp: {fileID: 1232869273}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 0
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 1, g: 1, b: 1, a: 0}
    ColorEnd: {r: 1, g: 1, b: 1, a: 1}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [White] [Image]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 199
    SequenceType: 0
    EaseType: 1
    EasePower: 3
    TargetType: 3
    TargetComp: {fileID: 1618844396}
    Duration: 0.4
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 0
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 1, g: 1, b: 1, a: 0}
    ColorEnd: {r: 1, g: 1, b: 1, a: 1}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.01s [Wait 0.5s]
    StartTime: 0.01
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 221
    SequenceType: 1
    EaseType: 0
    EasePower: 0
    TargetType: 3
    TargetComp: {fileID: 1232869273}
    Duration: 0.5
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: 
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 0
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 0
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 0, y: 0, z: 0}
    LocalEulerAnglesState: 0
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 0}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 0
    ColorStart: {r: 1, g: 1, b: 1, a: 0}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  - AtTime: At 0.51s [Home] [LoadScene]
    StartTime: 0.51
    TriggerStart: 0
    TriggerEnd: 0
    PropertyRectHeight: 20
    PropertyRectY: 243
    SequenceType: 5
    EaseType: 1
    EasePower: 3
    TargetType: 1
    TargetComp: {fileID: 908745315}
    Duration: 0
    EventDynamic:
      m_PersistentCalls:
        m_Calls: []
    IsUnfolded: 0
    IsDone: 0
    Target: {fileID: 0}
    IsActivating: 0
    PlaySFXBy: 0
    SFXFile: {fileID: 0}
    SFXIndex: 0
    SceneToLoad: Home
    Event:
      m_PersistentCalls:
        m_Calls: []
    TargetRtTask: 6
    AnchoredPositionState: 0
    AnchoredPositionStart: {x: 0, y: 0, z: 0}
    AnchoredPositionEnd: {x: 0, y: 0, z: 0}
    LocalScaleState: 1
    LocalScaleStart: {x: 0, y: 0, z: 0}
    LocalScaleEnd: {x: 1, y: 1, z: 1}
    LocalEulerAnglesState: 1
    LocalEulerAnglesStart: {x: 0, y: 0, z: 0}
    LocalEulerAnglesEnd: {x: 0, y: 0, z: 180}
    SizeDeltaState: 0
    SizeDeltaStart: {x: 0, y: 0, z: 0}
    SizeDeltaEnd: {x: 0, y: 0, z: 0}
    AnchorMinState: 0
    AnchorMinStart: {x: 0, y: 0, z: 0}
    AnchorMinEnd: {x: 0, y: 0, z: 0}
    AnchorMaxState: 0
    AnchorMaxStart: {x: 0, y: 0, z: 0}
    AnchorMaxEnd: {x: 0, y: 0, z: 0}
    PivotState: 0
    PivotStart: {x: 0, y: 0, z: 0}
    PivotEnd: {x: 0, y: 0, z: 0}
    TransState: 0
    TargetTransTask: 0
    LocalPositionState: 0
    LocalPositionStart: {x: 0, y: 0, z: 0}
    LocalPositionEnd: {x: 0, y: 0, z: 0}
    ImgState: 0
    TargetImgTask: 1
    ColorState: 1
    ColorStart: {r: 1, g: 1, b: 1, a: 1}
    ColorEnd: {r: 1, g: 1, b: 1, a: 0}
    FillAmountState: 0
    FillAmountStart: 0
    FillAmountEnd: 0
    TargetCgTask: 0
    AlphaState: 0
    AlphaStart: 0
    AlphaEnd: 0
    TargetCamTask: 0
    BackgroundColorState: 0
    BackgroundColorStart: {r: 0, g: 0, b: 0, a: 0}
    BackgroundColorEnd: {r: 0, g: 0, b: 0, a: 0}
    OrthographicSizeState: 0
    OrthographicSizeStart: 0
    OrthographicSizeEnd: 0
    TargetTextMeshProTask: 1
    TextMeshProColorState: 0
    TextMeshProColorStart: {r: 0, g: 0, b: 0, a: 0}
    TextMeshProColorEnd: {r: 0, g: 0, b: 0, a: 0}
    MaxVisibleCharactersState: 0
    MaxVisibleCharactersStart: 0
    MaxVisibleCharactersEnd: 0
    TestInt: 0
  PlayOnStart: 0
  CurrentTime: 0
  IsPlayingInEditMode: 0
--- !u!1 &1590290163
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1590290164}
  - component: {fileID: 1590290167}
  - component: {fileID: 1590290166}
  - component: {fileID: 1590290165}
  m_Layer: 5
  m_Name: Home
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1590290164
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590290163}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 206877568}
  m_Father: {fileID: 1512884867}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0}
  m_AnchorMax: {x: 0.5, y: 0}
  m_AnchoredPosition: {x: 0, y: 270}
  m_SizeDelta: {x: 250, y: 89}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1590290165
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590290163}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_HighlightedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_PressedColor: {r: 1, g: 1, b: 1, a: 1}
    m_SelectedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1590290166}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 206877569}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1514292393}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1590290166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590290163}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.2509804, g: 0.26666668, b: 0.34117648, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: c566cda3cafcb594397d8ad8306e4916, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1590290167
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590290163}
  m_CullTransparentMesh: 1
--- !u!1 &1618844395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1618844396}
  - component: {fileID: 1618844398}
  - component: {fileID: 1618844397}
  m_Layer: 5
  m_Name: White
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1618844396
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1618844395}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1748108035}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1618844397
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1618844395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 7482667652216324306, guid: af1a0da324ed10c47b60c8e9945a55ef, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 0
  m_FillAmount: 0
  m_FillClockwise: 1
  m_FillOrigin: 1
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1618844398
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1618844395}
  m_CullTransparentMesh: 1
--- !u!1 &1630439080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1630439081}
  - component: {fileID: 1630439083}
  - component: {fileID: 1630439082}
  - component: {fileID: 1630439084}
  m_Layer: 5
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1630439081
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630439080}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1304071251}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 560.0139, y: 220.65103}
  m_SizeDelta: {x: 120.5517, y: 120.5517}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1630439082
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630439080}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1630439083
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630439080}
  m_CullTransparentMesh: 1
--- !u!114 &1630439084
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630439080}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_HighlightedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_PressedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_SelectedColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_DisabledColor: {r: 0.9411765, g: 0.41960785, b: 0.6509804, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1630439082}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1684212274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1684212275}
  - component: {fileID: 1684212277}
  - component: {fileID: 1684212276}
  - component: {fileID: 1684212278}
  m_Layer: 5
  m_Name: Box (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1684212275
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1684212274}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1150971455}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 552.18, y: -138}
  m_SizeDelta: {x: 179.2479, y: 179.249}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1684212276
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1684212274}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1684212277
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1684212274}
  m_CullTransparentMesh: 1
--- !u!114 &1684212278
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1684212274}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 785ccc2bba337ec40987f8e05e6191d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_HighlightedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_PressedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_SelectedColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_DisabledColor: {r: 0.38039216, g: 0.9490196, b: 0.96862745, a: 1}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1684212276}
  _textColors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.15
  _scales:
    m_NormalScale: 1
    m_HighlightedScale: 1.1
    m_PressedScale: 1.2
    m_SelectedScale: 1.1
    m_DisabledScale: 1
    m_ScaleMultiplier: 1
    m_FadeDuration: 0.15
  _text: {fileID: 0}
  _onClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 221682841}
        m_TargetAssemblyTypeName: AnimationUI, Assembly-CSharp
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _onPointerEnter:
    m_PersistentCalls:
      m_Calls: []
  _onPointerExit:
    m_PersistentCalls:
      m_Calls: []
  _onPointerDown:
    m_PersistentCalls:
      m_Calls: []
  _onPointerUp:
    m_PersistentCalls:
      m_Calls: []
  _onSelect:
    m_PersistentCalls:
      m_Calls: []
  _onDeselect:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1713836259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1713836260}
  m_Layer: 0
  m_Name: AnimationUIs
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1713836260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1713836259}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 821842491}
  - {fileID: 1421636217}
  - {fileID: 221682840}
  - {fileID: 1514292392}
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1748108034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1748108035}
  - component: {fileID: 1748108037}
  - component: {fileID: 1748108036}
  m_Layer: 5
  m_Name: Blocker
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1748108035
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748108034}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.5, y: 1.5, z: 1.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1971658823}
  - {fileID: 1493417136}
  - {fileID: 1618844396}
  m_Father: {fileID: 1054948136}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1748108036
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748108034}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.4}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1748108037
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748108034}
  m_CullTransparentMesh: 1
--- !u!1 &1788952089
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1788952090}
  m_Layer: 5
  m_Name: TransitionStart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1788952090
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788952089}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 39153220}
  m_Father: {fileID: 292233844}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1846112382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1846112383}
  - component: {fileID: 1846112385}
  - component: {fileID: 1846112384}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1846112383
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1846112382}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1407296032}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1846112384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1846112382}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Prev
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_sharedMaterial: {fileID: -3388997479985545262, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1846112385
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1846112382}
  m_CullTransparentMesh: 1
--- !u!1 &1930784523
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1930784524}
  - component: {fileID: 1930784526}
  - component: {fileID: 1930784525}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1930784524
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1930784523}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1493417136}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1930784525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1930784523}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Next
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_sharedMaterial: {fileID: -3388997479985545262, guid: 5f5466f9f1f545f43ae490ad0ec5f013, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 60
  m_fontSizeBase: 60
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1930784526
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1930784523}
  m_CullTransparentMesh: 1
--- !u!1 &1971658822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1971658823}
  - component: {fileID: 1971658825}
  - component: {fileID: 1971658824}
  m_Layer: 5
  m_Name: LoseBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1971658823
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971658822}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1748108035}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 732, y: 734}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1971658824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971658822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: f92d2c88c5dc0eb4ab7ba5c6eca20ed7, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1971658825
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971658822}
  m_CullTransparentMesh: 1
