using UnityEngine;
using DhafinFawwaz.AnimationUILib;

/// <summary>
/// Troubleshooting guide and diagnostic tools for the UI Animation system.
/// Use this to identify and fix common animation issues.
/// </summary>
public class AnimationTroubleshootingGuide : MonoBehaviour
{
    [Header("Troubleshooting Guide")]
    [TextArea(15, 25)]
    public string troubleshootingInfo = @"
🔧 UI ANIMATION TROUBLESHOOTING GUIDE

COMMON ISSUES & SOLUTIONS:

1. 'NullReferenceException: routine is null' Error:
   ❌ Problem: AnimationUI.Play() called before proper initialization or too rapidly
   ✅ Solution:
      - Add AnimationUIHelper component to AnimationUI GameObjects (AUTO-FIX AVAILABLE)
      - Increase 'Min Time Between Triggers' in UIAnimationController (default: 0.1s)
      - Use 'Reset All Animation States' context menu option
      - Use 'Add AnimationUIHelper to All AnimationUI' for instant fix

2. Animations Not Triggering:
   ❌ Problem: Animation triggers not configured correctly
   ✅ Solution:
      - Check trigger conditions in UIAnimationController
      - Verify zone names match exactly (case-sensitive)
      - Use 'Debug Animation Status' context menu option
      - Enable debug logging in UIAnimationController

3. Animations Playing Too Frequently:
   ❌ Problem: Rapid-fire triggers causing conflicts
   ✅ Solution:
      - Increase 'Min Time Between Triggers' (default: 0.1s)
      - Check for duplicate animation triggers
      - Use 'Reset Animation States' to clear conflicts

4. AnimationUI Not Ready:
   ❌ Problem: GameObject inactive or component disabled
   ✅ Solution:
      - Ensure AnimationUI GameObject is active
      - Ensure AnimationUI component is enabled
      - Add AnimationUIHelper for better initialization

5. Zone Filtering Not Working:
   ❌ Problem: Zone names don't match
   ✅ Solution:
      - Check exact zone names (case-sensitive)
      - Use 'Print Zone Names' context menu option
      - Verify 'Filter By Zone' is checked

DIAGNOSTIC TOOLS:
- UIAnimationController → 'Debug Animation Status'
- UIAnimationController → 'Reset Animation States'
- AnimationUIHelper → 'Check Animation Status'
- DetectionZonePanelManager → 'Print Zone Names'
- GameManager → 'Print Current Stage Info'

PREVENTION TIPS:
- Always add AnimationUIHelper to AnimationUI GameObjects
- Set reasonable delays between triggers (0.1s minimum)
- Enable debug logging during setup
- Test animations individually before complex flows
- Use context menu testing options frequently
";

    [Header("Quick Diagnostics")]
    public UIAnimationController animationController;
    public DetectionZonePanelManager panelManager;

    [ContextMenu("Run Full Diagnostic")]
    public void RunFullDiagnostic()
    {
        Debug.Log("🔍 RUNNING FULL ANIMATION DIAGNOSTIC...");
        
        // Find components if not assigned
        if (animationController == null)
            animationController = FindObjectOfType<UIAnimationController>();
        if (panelManager == null)
            panelManager = FindObjectOfType<DetectionZonePanelManager>();
            
        // Check UIAnimationController
        CheckAnimationController();
        
        // Check AnimationUI components
        CheckAllAnimationUIs();
        
        // Check DetectionZonePanelManager
        CheckPanelManager();
        
        // Check GameManager
        CheckGameManager();
        
        Debug.Log("✅ DIAGNOSTIC COMPLETE - Check console for details");
    }
    
    void CheckAnimationController()
    {
        Debug.Log("--- CHECKING UI ANIMATION CONTROLLER ---");
        
        if (animationController == null)
        {
            Debug.LogError("❌ No UIAnimationController found in scene");
            return;
        }
        
        Debug.Log($"✅ UIAnimationController found on '{animationController.name}'");
        Debug.Log($"   - Animation triggers: {animationController.animationTriggers?.Length ?? 0}");
        Debug.Log($"   - Min time between triggers: {animationController.minTimeBetweenTriggers}s");
        Debug.Log($"   - Debug logging: {animationController.enableDebugLogging}");
        
        if (animationController.animationTriggers != null)
        {
            int validTriggers = 0;
            foreach (var trigger in animationController.animationTriggers)
            {
                if (trigger.animationUI != null) validTriggers++;
            }
            Debug.Log($"   - Valid triggers: {validTriggers}/{animationController.animationTriggers.Length}");
        }
    }
    
    void CheckAllAnimationUIs()
    {
        Debug.Log("--- CHECKING ANIMATION UI COMPONENTS ---");
        
        var allAnimationUIs = FindObjectsOfType<AnimationUI>();
        Debug.Log($"Found {allAnimationUIs.Length} AnimationUI components in scene");
        
        int readyCount = 0;
        int withHelperCount = 0;
        
        foreach (var animUI in allAnimationUIs)
        {
            var helper = animUI.GetComponent<AnimationUIHelper>();
            bool hasHelper = helper != null;
            bool isReady = hasHelper ? helper.CanPlayAnimation() : animUI.gameObject.activeInHierarchy && animUI.enabled;
            
            if (hasHelper) withHelperCount++;
            if (isReady) readyCount++;
            
            string status = hasHelper ? helper.GetStatus() : (isReady ? "READY" : "NOT READY");
            Debug.Log($"   - {animUI.name}: {status} {(hasHelper ? "(with helper)" : "(no helper)")}");
        }
        
        Debug.Log($"   - Ready for animation: {readyCount}/{allAnimationUIs.Length}");
        Debug.Log($"   - With AnimationUIHelper: {withHelperCount}/{allAnimationUIs.Length}");
        
        if (withHelperCount < allAnimationUIs.Length)
        {
            Debug.LogWarning("⚠️ Consider adding AnimationUIHelper to all AnimationUI components for better error handling");
        }
    }
    
    void CheckPanelManager()
    {
        Debug.Log("--- CHECKING DETECTION ZONE PANEL MANAGER ---");
        
        if (panelManager == null)
        {
            Debug.LogWarning("⚠️ No DetectionZonePanelManager found in scene");
            return;
        }
        
        Debug.Log($"✅ DetectionZonePanelManager found on '{panelManager.name}'");
        Debug.Log($"   - Animation controller assigned: {panelManager.animationController != null}");
        Debug.Log($"   - Debug logging: {panelManager.enableDebugLogging}");
        
        if (panelManager.animationController == null)
        {
            Debug.LogWarning("⚠️ DetectionZonePanelManager.animationController is not assigned");
        }
    }
    
    void CheckGameManager()
    {
        Debug.Log("--- CHECKING GAME MANAGER ---");
        
        if (GameManager.Instance == null)
        {
            Debug.LogWarning("⚠️ No GameManager found in scene");
            return;
        }
        
        Debug.Log($"✅ GameManager found");
        Debug.Log($"   - Current stage: {GameManager.Instance.GetCurrentStageString()}");
        Debug.Log($"   - Animation controller assigned: {GameManager.Instance.animationController != null}");
        Debug.Log($"   - Max sub-stages: [{string.Join(", ", GameManager.Instance.maxSubStagesPerStage)}]");
        
        if (GameManager.Instance.animationController == null)
        {
            Debug.LogWarning("⚠️ GameManager.animationController is not assigned");
        }
    }
    
    [ContextMenu("Fix Common Issues")]
    public void FixCommonIssues()
    {
        Debug.Log("🔧 ATTEMPTING TO FIX COMMON ISSUES...");
        
        // Reset animation states
        if (animationController != null)
        {
            animationController.ResetAnimationStates();
            Debug.Log("✅ Reset animation trigger times");
        }
        
        // Add AnimationUIHelper to AnimationUI components that don't have it
        var allAnimationUIs = FindObjectsOfType<AnimationUI>();
        int helpersAdded = 0;
        
        foreach (var animUI in allAnimationUIs)
        {
            if (animUI.GetComponent<AnimationUIHelper>() == null)
            {
                animUI.gameObject.AddComponent<AnimationUIHelper>();
                helpersAdded++;
            }
        }
        
        if (helpersAdded > 0)
        {
            Debug.Log($"✅ Added AnimationUIHelper to {helpersAdded} AnimationUI components");
        }
        
        // Auto-assign animation controller references
        if (animationController != null)
        {
            if (panelManager != null && panelManager.animationController == null)
            {
                panelManager.animationController = animationController;
                Debug.Log("✅ Assigned animation controller to DetectionZonePanelManager");
            }
            
            if (GameManager.Instance != null && GameManager.Instance.animationController == null)
            {
                GameManager.Instance.animationController = animationController;
                Debug.Log("✅ Assigned animation controller to GameManager");
            }
        }
        
        Debug.Log("🎉 COMMON ISSUES FIX COMPLETE");
    }
    
    [ContextMenu("Print Zone Names")]
    public void PrintZoneNames()
    {
        if (panelManager?.detectionZone?.detectionZones != null)
        {
            Debug.Log("=== DETECTION ZONE NAMES ===");
            foreach (var zone in panelManager.detectionZone.detectionZones)
            {
                Debug.Log($"  - '{zone.zoneName}' (Active: {zone.isActive})");
            }
            Debug.Log("============================");
        }
        else
        {
            Debug.LogWarning("No detection zones found");
        }
    }

    [ContextMenu("Add AnimationUIHelper to All AnimationUI")]
    public void AddHelpersToAllAnimationUI()
    {
        var allAnimationUIs = FindObjectsOfType<DhafinFawwaz.AnimationUILib.AnimationUI>();
        int helpersAdded = 0;

        foreach (var animUI in allAnimationUIs)
        {
            if (animUI.GetComponent<AnimationUIHelper>() == null)
            {
                var helper = animUI.gameObject.AddComponent<AnimationUIHelper>();
                helper.enableDebugLogging = true; // Enable debug logging by default
                helpersAdded++;
            }
        }

        Debug.Log($"✅ Added AnimationUIHelper to {helpersAdded} AnimationUI components");
        if (helpersAdded > 0)
        {
            Debug.Log("💡 AnimationUIHelper components added with debug logging enabled. This should help prevent the 'routine is null' error.");
        }
    }

    [ContextMenu("Reset All Animation States")]
    public void ResetAllAnimationStates()
    {
        // Reset UIAnimationController states
        if (animationController != null)
        {
            animationController.ResetAnimationStates();
        }

        // Reset all AnimationUIHelper states
        var allHelpers = FindObjectsOfType<AnimationUIHelper>();
        foreach (var helper in allHelpers)
        {
            helper.ResetAnimationState();
        }

        Debug.Log($"✅ Reset animation states for UIAnimationController and {allHelpers.Length} AnimationUIHelper components");
    }
}
