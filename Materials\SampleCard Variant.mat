%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SampleCard Variant
  m_Shader: {fileID: -6465566751694194690, guid: 67d2dec18164c374b8b0230fd77e9b42,
    type: 3}
  m_Parent: {fileID: 2100000, guid: 605420c27f47c6d4c9cea9cc54bbd8d2, type: 2}
  m_ModifiedSerializedProperties: 2
  m_ValidKeywords:
  - _ALPHAPREMULTIPLY_ON
  - _ALPHATEST_ON
  - _SURFACE_TYPE_TRANSPARENT
  m_InvalidKeywords: []
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses:
  - MOTIONVECTORS
  - DepthOnly
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: f35cd5510aee910439c5e187d1f57e6a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Cull: 0
    m_Colors: []
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &2189084517016100040
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
