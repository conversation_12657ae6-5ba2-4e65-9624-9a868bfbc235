using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using Vuforia;
using Image = UnityEngine.UI.Image;

public class ContextualLeftPanelButton : MonoBehaviour
{
    [Header("UI References")]
    public Button leftPanelButton;
    public GameObject buttonContainer;
    
    [Header("Button Settings")]
    public Vector2 buttonPosition = new Vector2(50, 50);
    public Vector2 buttonSize = new Vector2(120, 40);
    public Color buttonColor = new Color(0.2f, 0.6f, 1f, 0.8f);
    public Color buttonTextColor = Color.white;
    public string buttonText = "Show Left Panel";
    
    [Header("References")]
    public DetectionZonePanelManager panelManager;
    public Canvas uiCanvas;
    
    [Header("Settings")]
    public float updateInterval = 0.1f;
    public bool showDebugInfo = false;

    private float lastUpdateTime = 0f;
    private string leftZoneName = "";
    private string rightZoneName = "";
    
    void Start()
    {
        // Find references if not assigned
        if (panelManager == null)
            panelManager = FindObjectOfType<DetectionZonePanelManager>();
            
        if (uiCanvas == null)
        {
            // Use stage 1 canvas from GameManager
            if (GameManager.Instance != null && GameManager.Instance.stageCanvases.Length > 0)
            {
                uiCanvas = GameManager.Instance.stageCanvases[0];
            }
            else
            {
                uiCanvas = FindObjectOfType<Canvas>();
            }
        }
        
        CreateButtonIfNeeded();
        DetermineZones();

        // Hide button by default
        if (buttonContainer != null)
        {
            buttonContainer.SetActive(false);
        }
    }
    
    void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            CheckButtonVisibility();
            lastUpdateTime = Time.time;
        }
    }
    
    void CreateButtonIfNeeded()
    {
        if (leftPanelButton != null) return;
        
        if (uiCanvas == null)
        {
            Debug.LogError("ContextualLeftPanelButton: No UI Canvas found!");
            return;
        }
        
        // Create button container
        GameObject containerObj = new GameObject("LeftPanelButtonContainer");
        containerObj.transform.SetParent(uiCanvas.transform, false);
        buttonContainer = containerObj;

        RectTransform containerRect = containerObj.AddComponent<RectTransform>();
        containerRect.anchorMin = Vector2.zero;
        containerRect.anchorMax = Vector2.zero;
        containerRect.pivot = Vector2.zero;
        containerRect.anchoredPosition = buttonPosition;
        containerRect.sizeDelta = buttonSize;
        
        // Create button
        GameObject buttonObj = new GameObject("LeftPanelButton");
        buttonObj.transform.SetParent(containerObj.transform, false);
        
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.anchorMin = Vector2.zero;
        buttonRect.anchorMax = Vector2.one;
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        // Add button components
        Image buttonImage = buttonObj.AddComponent<Image>();
        buttonImage.color = buttonColor;
        
        leftPanelButton = buttonObj.AddComponent<Button>();
        leftPanelButton.onClick.AddListener(OnLeftPanelButtonClicked);
        
        // Create button text
        GameObject textObj = new GameObject("ButtonText");
        textObj.transform.SetParent(buttonObj.transform, false);
        
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        TextMeshProUGUI buttonTextComponent = textObj.AddComponent<TextMeshProUGUI>();
        buttonTextComponent.text = buttonText;
        buttonTextComponent.fontSize = 12;
        buttonTextComponent.color = buttonTextColor;
        buttonTextComponent.alignment = TextAlignmentOptions.Center;
        
        if (showDebugInfo)
        {
            Debug.Log("ContextualLeftPanelButton: Button created successfully");
        }
    }
    
    void DetermineZones()
    {
        if (panelManager?.detectionZone?.detectionZones == null) return;

        // Find left and right zones by x position
        var zones = panelManager.detectionZone.detectionZones.OrderBy(z => z.x).ToList();

        if (zones.Count >= 2)
        {
            leftZoneName = zones[0].zoneName;  // Leftmost zone
            rightZoneName = zones[1].zoneName; // Rightmost zone (or second zone)

            if (showDebugInfo)
            {
                Debug.Log($"ContextualLeftPanelButton: Left zone: '{leftZoneName}', Right zone: '{rightZoneName}'");
            }
        }
        else if (zones.Count == 1)
        {
            // If only one zone, treat it as right zone
            rightZoneName = zones[0].zoneName;

            if (showDebugInfo)
            {
                Debug.Log($"ContextualLeftPanelButton: Only one zone found, treating as right zone: '{rightZoneName}'");
            }
        }
    }

    void CheckButtonVisibility()
    {
        if (panelManager == null || string.IsNullOrEmpty(rightZoneName))
        {
            SetButtonVisible(false);
            return;
        }

        bool shouldShow = ShouldShowButton();
        bool currentlyVisible = buttonContainer != null && buttonContainer.activeInHierarchy;

        if (shouldShow != currentlyVisible)
        {
            SetButtonVisible(shouldShow);

            if (showDebugInfo)
            {
                Debug.Log($"ContextualLeftPanelButton: Button visibility changed to {shouldShow}");
            }
        }

        // Update button text if visible
        if (currentlyVisible && panelManager != null)
        {
            UpdateButtonText();
        }
    }

    bool ShouldShowButton()
    {
        if (string.IsNullOrEmpty(rightZoneName)) return false;

        // Condition 1: Search results displayed in right zone
        bool hasSearchResults = HasSearchResultsInRightZone();

        // Condition 2: VuMark tracked in right zone
        bool hasVuMarkTracking = HasVuMarkTrackingInRightZone();

        bool shouldShow = hasSearchResults || hasVuMarkTracking;

        if (showDebugInfo)
        {
            Debug.Log($"ContextualLeftPanelButton: Search results in right zone: {hasSearchResults}, VuMark tracking in right zone: {hasVuMarkTracking}, Should show: {shouldShow}");
        }

        return shouldShow;
    }

    bool HasSearchResultsInRightZone()
    {
        if (panelManager == null || string.IsNullOrEmpty(rightZoneName)) return false;

        bool hasSearchData = panelManager.HasSearchDataInZone(rightZoneName);

        if (showDebugInfo && hasSearchData)
        {
            Debug.Log($"ContextualLeftPanelButton: Search results found in right zone '{rightZoneName}'");
        }

        return hasSearchData;
    }

    bool HasVuMarkTrackingInRightZone()
    {
        if (panelManager == null || string.IsNullOrEmpty(rightZoneName)) return false;

        bool hasVuMarkData = panelManager.HasVuMarkDataInZone(rightZoneName);

        if (showDebugInfo && hasVuMarkData)
        {
            Debug.Log($"ContextualLeftPanelButton: VuMark tracking found in right zone '{rightZoneName}'");
        }

        return hasVuMarkData;
    }

    bool HasRightPanelContent()
    {
        if (panelManager == null)
        {
            if (showDebugInfo)
                Debug.LogWarning("ContextualLeftPanelButton: panelManager is null");
            return false;
        }

        bool hasContent = panelManager.HasContentInRightPanels();

        if (showDebugInfo)
        {
            Debug.Log($"ContextualLeftPanelButton: HasContentInRightPanels() returned: {hasContent}");

            // Additional debug info
            string leftZone = panelManager.GetLeftZoneName();
            Debug.Log($"ContextualLeftPanelButton: Left zone name: '{leftZone}'");

            // Check individual zone content
            if (panelManager.detectionZone?.detectionZones != null)
            {
                foreach (var zone in panelManager.detectionZone.detectionZones)
                {
                    bool zoneHasContent = panelManager.HasContentInZone(zone.zoneName);
                    Debug.Log($"ContextualLeftPanelButton: Zone '{zone.zoneName}' has content: {zoneHasContent}");
                }
            }
        }

        return hasContent;
    }

    // Public method to force refresh button visibility (for debugging)
    public void RefreshButtonVisibility()
    {
        if (showDebugInfo)
        {
            Debug.Log("ContextualLeftPanelButton: Manual refresh triggered");
        }

        CheckButtonVisibility();
    }

    void SetButtonVisible(bool visible)
    {
        if (buttonContainer != null)
        {
            buttonContainer.SetActive(visible);

            if (showDebugInfo)
            {
                Debug.Log($"ContextualLeftPanelButton: SetButtonVisible({visible}) - GameObject.SetActive({visible})");
            }
        }
        else if (showDebugInfo)
        {
            Debug.LogWarning("ContextualLeftPanelButton: buttonContainer is null in SetButtonVisible!");
        }
    }

    // Public method to force button visible for testing
    public void ForceButtonVisible(bool visible)
    {
        if (showDebugInfo)
        {
            Debug.Log($"ContextualLeftPanelButton: ForceButtonVisible({visible}) called");
        }

        SetButtonVisible(visible);
    }

    void OnLeftPanelButtonClicked()
    {
        if (panelManager == null)
        {
            Debug.LogWarning("ContextualLeftPanelButton: Cannot toggle left panel - missing panel manager reference");
            return;
        }

        if (showDebugInfo)
        {
            Debug.Log("ContextualLeftPanelButton: Left panel button clicked - toggling left panel");
        }

        // Toggle the left panel visibility (this will enable/disable the detection zone)
        panelManager.ToggleLeftPanel();

        // Update button text based on panel state
        UpdateButtonText();

        if (showDebugInfo)
        {
            bool isLeftZoneActive = panelManager.detectionZone?.IsLeftDetectionZoneActive() ?? false;
            Debug.Log($"ContextualLeftPanelButton: After toggle - Left detection zone active: {isLeftZoneActive}, Panel visible: {panelManager.IsLeftPanelVisible()}");
        }
    }

    void UpdateButtonText()
    {
        if (leftPanelButton != null && panelManager != null)
        {
            var textComponent = leftPanelButton.GetComponentInChildren<TextMeshProUGUI>();
            if (textComponent != null)
            {
                bool isVisible = panelManager.IsLeftPanelVisible();
                textComponent.text = isVisible ? "Hide Left Zone" : "Show Left Zone";
            }
        }
    }





    // Public method to set the left zone name (for external configuration)
    public void SetLeftZoneName(string zoneName)
    {
        leftZoneName = zoneName;
        if (showDebugInfo)
        {
            Debug.Log($"ContextualLeftPanelButton: Left zone name set to '{zoneName}'");
        }
    }
}
