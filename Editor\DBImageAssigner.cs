using UnityEngine;
using UnityEditor;
using System.IO;

public class DBImageAssigner : EditorWindow
{
    public DBCoRe dbCore;
    public string productName;
    public Texture2D image;

    [MenuItem("Tools/Assign Image To Product In DB")]
    public static void ShowWindow()
    {
        GetWindow<DBImageAssigner>("Assign Image To Product");
    }

    void OnGUI()
    {
        dbCore = (DBCoRe)EditorGUILayout.ObjectField("DBCoRe Instance", dbCore, typeof(DBCoRe), true);
        productName = EditorGUILayout.TextField("Product Name", productName);
        image = (Texture2D)EditorGUILayout.ObjectField("Image", image, typeof(Texture2D), false);

        if (GUILayout.Button("Assign Image"))
        {
            if (dbCore == null || string.IsNullOrEmpty(productName) || image == null)
            {
                Debug.LogError("Please assign all fields.");
                return;
            }

            string path = AssetDatabase.GetAssetPath(image);
            byte[] imageBytes = File.ReadAllBytes(path);

            dbCore.AssignImageToProduct(productName, imageBytes);
            Debug.Log($"Assigned image to product '{productName}' in DB.");
        }
    }
}