using UnityEngine;

/// <summary>
/// Example controller showing how to use the sub-stage system for complex UI flows.
/// This demonstrates practical usage patterns for sub-stages within primary stages.
/// </summary>
public class SubStageExampleController : MonoBehaviour
{
    [Header("Example Sub-Stage Flow")]
    [TextArea(5, 10)]
    public string exampleFlow = @"
EXAMPLE SUB-STAGE FLOW:

Stage 1 (Product Discovery):
  1.1 - Empty state (no content)
  1.2 - VuMark detected (show product info)
  1.3 - Product selected (highlight selection)

Stage 2 (Comparison):
  2.1 - Single product view
  2.2 - Multiple products view
  2.3 - Comparison table view
  2.4 - Detailed comparison view

Stage 3 (Decision):
  3.1 - Review selections
  3.2 - Final comparison

Stage 4 (Completion):
  4.1 - Summary
  4.2 - Export/Save
  4.3 - Reset for next session
";

    [Header("Sub-Stage Management")]
    public bool autoProgressSubStages = false;
    public float autoProgressDelay = 3f;
    
    private float lastSubStageChangeTime;
    
    void Start()
    {
        lastSubStageChangeTime = Time.time;
        
        if (GameManager.Instance != null)
        {
            Debug.Log($"SubStageExample: Starting at {GameManager.Instance.GetCurrentStageString()}");
        }
    }
    
    void Update()
    {
        if (autoProgressSubStages && Time.time - lastSubStageChangeTime > autoProgressDelay)
        {
            ProgressToNextSubStage();
            lastSubStageChangeTime = Time.time;
        }
    }
    
    #region Sub-Stage Navigation
    
    public void ProgressToNextSubStage()
    {
        if (GameManager.Instance == null) return;
        
        int currentStage = GameManager.Instance.currentStage;
        int currentSubStage = GameManager.Instance.currentSubStage;
        int maxSubStages = GameManager.Instance.GetMaxSubStagesForCurrentStage();
        
        if (currentSubStage < maxSubStages)
        {
            // Move to next sub-stage within current stage
            GameManager.Instance.SetSubStage(currentSubStage + 1);
            Debug.Log($"SubStageExample: Progressed to {GameManager.Instance.GetCurrentStageString()}");
        }
        else
        {
            // Move to next primary stage
            if (currentStage < GameManager.Instance.stageCanvases.Length)
            {
                GameManager.Instance.SetStage(currentStage + 1);
                Debug.Log($"SubStageExample: Progressed to new stage {GameManager.Instance.GetCurrentStageString()}");
            }
            else
            {
                Debug.Log("SubStageExample: Reached final stage");
            }
        }
    }
    
    public void GoToPreviousSubStage()
    {
        if (GameManager.Instance == null) return;
        
        int currentStage = GameManager.Instance.currentStage;
        int currentSubStage = GameManager.Instance.currentSubStage;
        
        if (currentSubStage > 1)
        {
            // Move to previous sub-stage within current stage
            GameManager.Instance.SetSubStage(currentSubStage - 1);
            Debug.Log($"SubStageExample: Went back to {GameManager.Instance.GetCurrentStageString()}");
        }
        else if (currentStage > 1)
        {
            // Move to previous primary stage (last sub-stage)
            int prevStage = currentStage - 1;
            int maxSubStagesInPrevStage = GameManager.Instance.GetMaxSubStagesForStage(prevStage);
            GameManager.Instance.SetStageAndSubStage(prevStage, maxSubStagesInPrevStage);
            Debug.Log($"SubStageExample: Went back to previous stage {GameManager.Instance.GetCurrentStageString()}");
        }
        else
        {
            Debug.Log("SubStageExample: Already at first stage and sub-stage");
        }
    }
    
    public void GoToSpecificSubStage(int stage, int subStage)
    {
        if (GameManager.Instance == null) return;
        
        GameManager.Instance.SetStageAndSubStage(stage, subStage);
        Debug.Log($"SubStageExample: Jumped to {GameManager.Instance.GetCurrentStageString()}");
    }
    
    #endregion
    
    #region Example Use Cases
    
    /// <summary>
    /// Example: When VuMark is detected, progress to product info sub-stage
    /// </summary>
    public void OnVuMarkDetected()
    {
        if (GameManager.Instance != null && GameManager.Instance.currentStage == 1)
        {
            // In Stage 1 (Product Discovery), move to sub-stage 2 (VuMark detected)
            GameManager.Instance.SetSubStage(2);
            Debug.Log("SubStageExample: VuMark detected, showing product info");
        }
    }
    
    /// <summary>
    /// Example: When product is selected, progress to selection highlight sub-stage
    /// </summary>
    public void OnProductSelected()
    {
        if (GameManager.Instance != null && GameManager.Instance.currentStage == 1)
        {
            // In Stage 1 (Product Discovery), move to sub-stage 3 (Product selected)
            GameManager.Instance.SetSubStage(3);
            Debug.Log("SubStageExample: Product selected, highlighting selection");
        }
    }
    
    /// <summary>
    /// Example: When multiple products are selected, switch to comparison mode
    /// </summary>
    public void OnMultipleProductsSelected()
    {
        if (GameManager.Instance != null)
        {
            // Move to Stage 2 (Comparison), sub-stage 2 (Multiple products view)
            GameManager.Instance.SetStageAndSubStage(2, 2);
            Debug.Log("SubStageExample: Multiple products selected, entering comparison mode");
        }
    }
    
    /// <summary>
    /// Example: When comparison is complete, move to decision stage
    /// </summary>
    public void OnComparisonComplete()
    {
        if (GameManager.Instance != null)
        {
            // Move to Stage 3 (Decision), sub-stage 1 (Review selections)
            GameManager.Instance.SetStageAndSubStage(3, 1);
            Debug.Log("SubStageExample: Comparison complete, reviewing selections");
        }
    }
    
    #endregion
    
    #region Context Menu Helpers
    
    [ContextMenu("Example: Product Discovery Flow")]
    public void ExampleProductDiscoveryFlow()
    {
        StartCoroutine(RunProductDiscoveryFlow());
    }
    
    [ContextMenu("Example: Comparison Flow")]
    public void ExampleComparisonFlow()
    {
        StartCoroutine(RunComparisonFlow());
    }
    
    [ContextMenu("Reset to Stage 1.1")]
    public void ResetToStart()
    {
        if (GameManager.Instance != null)
        {
            GameManager.Instance.SetStageAndSubStage(1, 1);
            Debug.Log("SubStageExample: Reset to beginning");
        }
    }
    
    private System.Collections.IEnumerator RunProductDiscoveryFlow()
    {
        Debug.Log("=== RUNNING PRODUCT DISCOVERY FLOW ===");
        
        // Start at Stage 1.1 (Empty state)
        GoToSpecificSubStage(1, 1);
        yield return new WaitForSeconds(2f);
        
        // Simulate VuMark detection -> Stage 1.2
        Debug.Log("Simulating VuMark detection...");
        OnVuMarkDetected();
        yield return new WaitForSeconds(2f);
        
        // Simulate product selection -> Stage 1.3
        Debug.Log("Simulating product selection...");
        OnProductSelected();
        yield return new WaitForSeconds(2f);
        
        Debug.Log("=== PRODUCT DISCOVERY FLOW COMPLETE ===");
    }
    
    private System.Collections.IEnumerator RunComparisonFlow()
    {
        Debug.Log("=== RUNNING COMPARISON FLOW ===");
        
        // Start at Stage 2.1 (Single product view)
        GoToSpecificSubStage(2, 1);
        yield return new WaitForSeconds(2f);
        
        // Move to Stage 2.2 (Multiple products view)
        Debug.Log("Adding more products...");
        OnMultipleProductsSelected();
        yield return new WaitForSeconds(2f);
        
        // Move to Stage 2.3 (Comparison table view)
        Debug.Log("Showing comparison table...");
        GoToSpecificSubStage(2, 3);
        yield return new WaitForSeconds(2f);
        
        // Move to Stage 2.4 (Detailed comparison view)
        Debug.Log("Showing detailed comparison...");
        GoToSpecificSubStage(2, 4);
        yield return new WaitForSeconds(2f);
        
        // Complete comparison -> Stage 3.1
        Debug.Log("Completing comparison...");
        OnComparisonComplete();
        
        Debug.Log("=== COMPARISON FLOW COMPLETE ===");
    }
    
    #endregion
}
