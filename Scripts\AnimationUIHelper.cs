using UnityEngine;
using DhafinFawwaz.AnimationUILib;

/// <summary>
/// Helper script to safely manage AnimationUI components and prevent common errors.
/// Attach this to GameObjects with AnimationUI components for better error handling.
/// </summary>
public class AnimationUIHelper : MonoBehaviour
{
    [<PERSON><PERSON>("Animation UI Reference")]
    public AnimationUI animationUI;
    
    [<PERSON><PERSON>("Safety Settings")]
    [Tooltip("Wait for this many frames before allowing animation playback (helps ensure proper initialization)")]
    public int initializationFrames = 2;

    [<PERSON><PERSON><PERSON>("Enable debug logging for this AnimationUI")]
    public bool enableDebugLogging = false;

    [Tooltip("Use fallback method if AnimationUI.Play() fails (bypasses library bugs)")]
    public bool useFallbackOnError = true;
    
    private bool isInitialized = false;
    private int frameCount = 0;
    
    void Awake()
    {
        // Auto-find AnimationUI if not assigned
        if (animationUI == null)
        {
            animationUI = GetComponent<AnimationUI>();
        }
        
        if (animationUI == null)
        {
            Debug.LogError($"AnimationUIHelper: No AnimationUI component found on '{gameObject.name}'");
        }
    }
    
    void Start()
    {
        frameCount = 0;
        isInitialized = false;
        
        if (enableDebugLogging)
        {
            Debug.Log($"AnimationUIHelper: Starting initialization for '{gameObject.name}'");
        }
    }
    
    void Update()
    {
        if (!isInitialized && frameCount < initializationFrames)
        {
            frameCount++;
            if (frameCount >= initializationFrames)
            {
                isInitialized = true;
                if (enableDebugLogging)
                {
                    Debug.Log($"AnimationUIHelper: '{gameObject.name}' is now initialized and ready for animation");
                }
            }
        }
    }
    
    /// <summary>
    /// Safely play the animation with error checking and coroutine management
    /// </summary>
    public void SafePlay()
    {
        if (!CanPlayAnimation())
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Cannot play animation on '{gameObject.name}' - not ready");
            return;
        }

        StartCoroutine(SafePlayCoroutine(false));
    }

    /// <summary>
    /// Simple fallback method that tries the basic AnimationUI.Play() with error handling
    /// </summary>
    public void SimpleSafePlay()
    {
        if (!CanPlayAnimation()) return;

        try
        {
            if (enableDebugLogging)
                Debug.Log($"AnimationUIHelper: Simple safe play on '{gameObject.name}'");

            animationUI.Play();
        }
        catch (System.Exception e)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Simple play failed on '{gameObject.name}': {e.Message}");
        }
    }

    /// <summary>
    /// Simple fallback method that tries the basic AnimationUI.PlayReversed() with error handling
    /// </summary>
    public void SimpleSafePlayReversed()
    {
        if (!CanPlayAnimation()) return;

        try
        {
            if (enableDebugLogging)
                Debug.Log($"AnimationUIHelper: Simple safe play reversed on '{gameObject.name}'");

            animationUI.PlayReversed();
        }
        catch (System.Exception e)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Simple play reversed failed on '{gameObject.name}': {e.Message}");
        }
    }

    /// <summary>
    /// Internal coroutine for safe animation playback - simplified to avoid try-catch with yield
    /// </summary>
    private System.Collections.IEnumerator SafePlayCoroutine(bool reverse)
    {
        if (animationUI == null) yield break;

        if (enableDebugLogging)
            Debug.Log($"AnimationUIHelper: {(reverse ? "Reverse " : "")}Playing animation on '{gameObject.name}'");

        // Wait multiple frames to ensure proper initialization
        yield return null;
        yield return null;

        // Try to play the animation safely
        bool success = TryPlayAnimationSafely(reverse);

        if (!success && enableDebugLogging)
        {
            Debug.LogWarning($"AnimationUIHelper: Failed to play animation safely on '{gameObject.name}'");
        }
    }

    /// <summary>
    /// Attempts to play animation safely without using coroutines (to avoid try-catch yield issues)
    /// </summary>
    private bool TryPlayAnimationSafely(bool reverse)
    {
        if (animationUI == null) return false;

        try
        {
            // Simple approach: just call the AnimationUI methods with error handling
            if (reverse)
            {
                animationUI.PlayReversed();
            }
            else
            {
                animationUI.Play();
            }
            return true;
        }
        catch (System.Exception e)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Animation play failed on '{gameObject.name}': {e.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Safely play the animation in reverse with error checking
    /// </summary>
    public void SafePlayReversed()
    {
        if (!CanPlayAnimation())
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Cannot play reverse animation on '{gameObject.name}' - not ready");
            return;
        }

        StartCoroutine(SafePlayCoroutine(true));
    }
    
    /// <summary>
    /// Safely stop the animation with error checking
    /// </summary>
    public void SafeStop()
    {
        if (animationUI == null) return;

        SafeStopInternal();
    }

    /// <summary>
    /// Internal safe stop method that handles errors gracefully
    /// </summary>
    private void SafeStopInternal()
    {
        if (animationUI == null) return;

        try
        {
            if (enableDebugLogging)
                Debug.Log($"AnimationUIHelper: Stopping animation on '{gameObject.name}'");

            // Simple approach: call the AnimationUI Stop method with error handling
            animationUI.Stop();
        }
        catch (System.Exception e)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: Stop failed on '{gameObject.name}': {e.Message}");

            // If the built-in Stop() fails, try stopping all coroutines on this GameObject
            try
            {
                StopAllCoroutines();
            }
            catch (System.Exception e2)
            {
                Debug.LogError($"AnimationUIHelper: Critical error stopping animation on '{gameObject.name}': {e2.Message}");
            }
        }
    }
    
    /// <summary>
    /// Check if the animation is ready to play
    /// </summary>
    public bool CanPlayAnimation()
    {
        if (animationUI == null)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: AnimationUI is null on '{gameObject.name}'");
            return false;
        }
        
        if (!gameObject.activeInHierarchy)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: GameObject '{gameObject.name}' is not active in hierarchy");
            return false;
        }
        
        if (!animationUI.enabled)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: AnimationUI component is disabled on '{gameObject.name}'");
            return false;
        }
        
        if (!isInitialized)
        {
            if (enableDebugLogging)
                Debug.LogWarning($"AnimationUIHelper: AnimationUI '{gameObject.name}' is not yet initialized (frame {frameCount}/{initializationFrames})");
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// Get the current status of this AnimationUI
    /// </summary>
    public string GetStatus()
    {
        if (animationUI == null) return "NULL";
        if (!gameObject.activeInHierarchy) return "INACTIVE";
        if (!animationUI.enabled) return "DISABLED";
        if (!isInitialized) return $"INITIALIZING ({frameCount}/{initializationFrames})";
        return "READY";
    }
    
    #region Context Menu Helpers
    
    [ContextMenu("Test Safe Play")]
    public void TestSafePlay()
    {
        SafePlay();
    }

    [ContextMenu("Test Safe Play Reversed")]
    public void TestSafePlayReversed()
    {
        SafePlayReversed();
    }

    [ContextMenu("Test Safe Stop")]
    public void TestSafeStop()
    {
        SafeStop();
    }

    [ContextMenu("Test Simple Safe Play")]
    public void TestSimpleSafePlay()
    {
        SimpleSafePlay();
    }

    [ContextMenu("Test Simple Safe Play Reversed")]
    public void TestSimpleSafePlayReversed()
    {
        SimpleSafePlayReversed();
    }

    [ContextMenu("Test Rapid Fire (Should Not Crash)")]
    public void TestRapidFire()
    {
        StartCoroutine(RapidFireTest());
    }

    private System.Collections.IEnumerator RapidFireTest()
    {
        Debug.Log($"AnimationUIHelper: Starting rapid fire test on '{gameObject.name}'");

        for (int i = 0; i < 10; i++)
        {
            SimpleSafePlay();
            yield return new WaitForSeconds(0.05f); // Very rapid triggers
            SimpleSafePlayReversed();
            yield return new WaitForSeconds(0.05f);
        }

        Debug.Log($"AnimationUIHelper: Rapid fire test completed on '{gameObject.name}' - no crashes!");
    }
    
    [ContextMenu("Check Animation Status")]
    public void CheckAnimationStatus()
    {
        Debug.Log($"AnimationUIHelper Status for '{gameObject.name}': {GetStatus()}");
        Debug.Log($"  - AnimationUI: {(animationUI != null ? "Found" : "NULL")}");
        Debug.Log($"  - GameObject Active: {gameObject.activeInHierarchy}");
        Debug.Log($"  - Component Enabled: {(animationUI != null ? animationUI.enabled.ToString() : "N/A")}");
        Debug.Log($"  - Initialized: {isInitialized} (Frame {frameCount}/{initializationFrames})");
        Debug.Log($"  - Can Play: {CanPlayAnimation()}");
    }
    
    [ContextMenu("Force Initialize")]
    public void ForceInitialize()
    {
        frameCount = initializationFrames;
        isInitialized = true;
        Debug.Log($"AnimationUIHelper: Force initialized '{gameObject.name}'");
    }

    [ContextMenu("Reset Animation State")]
    public void ResetAnimationState()
    {
        try
        {
            // Stop all coroutines on this GameObject
            StopAllCoroutines();

            // Reset initialization state
            frameCount = 0;
            isInitialized = false;

            Debug.Log($"AnimationUIHelper: Reset animation state for '{gameObject.name}'");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"AnimationUIHelper: Error resetting animation state on '{gameObject.name}': {e.Message}");
        }
    }
    
    #endregion
}
