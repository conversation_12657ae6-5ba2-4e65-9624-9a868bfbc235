fileFormatVersion: 2
guid: 7aacebecbde8fe7429df5b562f4d31b6
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Breathing Idle
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 283
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton:
    - name: robot_0427091207_texture@Breathing Idle(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Hips
      parentName: robot_0427091207_texture@Breathing Idle(Clone)
      position: {x: -0.00095203705, y: 0.7997965, z: -0.0074644545}
      rotation: {x: -0.017764915, y: 0.004787935, z: 0.009113698, w: 0.9997892}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine
      parentName: mixamorig:Hips
      position: {x: -0, y: 0.09049, z: -0.016127}
      rotation: {x: -0.07860924, y: 0.0014068892, z: 0.00034472864, w: 0.9969045}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine1
      parentName: mixamorig:Spine
      position: {x: -0, y: 0.107235, z: 0}
      rotation: {x: 0.010506376, y: 0.02282925, z: 0.002483657, w: 0.9996811}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine2
      parentName: mixamorig:Spine1
      position: {x: -0, y: 0.122554, z: 0}
      rotation: {x: 0.010433593, y: 0.02286545, z: 0.0020288147, w: 0.99968207}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Neck
      parentName: mixamorig:Spine2
      position: {x: -0, y: 0.13787399, z: 0}
      rotation: {x: 0.07378588, y: -0.010611248, z: 0.010871597, w: 0.9971584}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Head
      parentName: mixamorig:Neck
      position: {x: -0, y: 0.287347, z: 0.020274999}
      rotation: {x: 0.045236066, y: 0.009748847, z: -0.03169065, w: 0.99842596}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:HeadTop_End
      parentName: mixamorig:Head
      position: {x: -0, y: 0.38738897, z: 0.027334}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftShoulder
      parentName: mixamorig:Spine2
      position: {x: -0.080674, y: 0.110259995, z: -0.0045149997}
      rotation: {x: 0.51441205, y: -0.457919, z: 0.6089758, w: 0.39349577}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftArm
      parentName: mixamorig:LeftShoulder
      position: {x: -0, y: 0.171266, z: 0}
      rotation: {x: -0.13505624, y: -0.13573906, z: -0.028780092, w: 0.98107415}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftForeArm
      parentName: mixamorig:LeftArm
      position: {x: -0, y: 0.19595699, z: 0}
      rotation: {x: -0.009164496, y: 0.06954921, z: -0.026384681, w: 0.99718744}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHand
      parentName: mixamorig:LeftForeArm
      position: {x: -0, y: 0.271399, z: 0}
      rotation: {x: 0.088434376, y: -0.1607276, z: 0.09759264, w: 0.97817266}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb1
      parentName: mixamorig:LeftHand
      position: {x: 0.052369997, y: 0.040117998, z: -0.000071}
      rotation: {x: -0.045978554, y: 0.01247626, z: -0.41599306, w: 0.90811896}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb2
      parentName: mixamorig:LeftHandThumb1
      position: {x: -0.010662, y: 0.051687997, z: 0}
      rotation: {x: 0.008375958, y: -0.031856023, z: 0.2444651, w: 0.9690985}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb3
      parentName: mixamorig:LeftHandThumb2
      position: {x: 0.012012, y: 0.053972, z: 0}
      rotation: {x: -0.020177357, y: 0.0029150306, z: -0.10285606, w: 0.99448735}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb4
      parentName: mixamorig:LeftHandThumb3
      position: {x: -0.001351, y: 0.040544, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex1
      parentName: mixamorig:LeftHand
      position: {x: 0.057484, y: 0.14182499, z: -0.000538}
      rotation: {x: 0.08245662, y: 0.0053982404, z: 0.011180804, w: 0.9965173}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex2
      parentName: mixamorig:LeftHandIndex1
      position: {x: -0, y: 0.057224, z: 0}
      rotation: {x: 0.027335757, y: -0.00000005587934, z: 0.003382553, w: 0.9996206}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex3
      parentName: mixamorig:LeftHandIndex2
      position: {x: -0.000001, y: 0.052487, z: 0}
      rotation: {x: 0.036651, y: -0.00000020712402, z: 0.004574061, w: 0.99931765}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex4
      parentName: mixamorig:LeftHandIndex3
      position: {x: 0.000001, y: 0.033013, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle1
      parentName: mixamorig:LeftHand
      position: {x: -0.0019029999, y: 0.15036699, z: 0.001516}
      rotation: {x: 0.07957595, y: 0.001815974, z: 0.014696134, w: 0.9967189}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle2
      parentName: mixamorig:LeftHandMiddle1
      position: {x: -0.000008999999, y: 0.051385, z: 0}
      rotation: {x: 0.032608353, y: 0.00000063993474, z: 0.00422763, w: 0.9994593}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle3
      parentName: mixamorig:LeftHandMiddle2
      position: {x: 0.000002, y: 0.053545997, z: 0}
      rotation: {x: 0.037650455, y: -0.000005629326, z: 0.004843265, w: 0.99927926}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle4
      parentName: mixamorig:LeftHandMiddle3
      position: {x: 0.0000069999996, y: 0.034042, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing1
      parentName: mixamorig:LeftHand
      position: {x: -0.055581998, y: 0.136089, z: -0.001109}
      rotation: {x: 0.07675942, y: 0.010881031, z: 0.013224455, w: 0.9969025}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing2
      parentName: mixamorig:LeftHandRing1
      position: {x: -0.000013999999, y: 0.045031, z: 0}
      rotation: {x: 0.036081184, y: 0.0000063301964, z: 0.0051278523, w: 0.9993357}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing3
      parentName: mixamorig:LeftHandRing2
      position: {x: 0.000003, y: 0.041426, z: 0}
      rotation: {x: 0.039309688, y: -0.000015506428, z: 0.005583197, w: 0.99921155}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing4
      parentName: mixamorig:LeftHandRing3
      position: {x: 0.000012, y: 0.024681998, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightShoulder
      parentName: mixamorig:Spine2
      position: {x: 0.080674, y: 0.1104, z: -0.005302}
      rotation: {x: 0.565279, y: 0.4352286, z: -0.5523436, w: 0.43122184}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightArm
      parentName: mixamorig:RightShoulder
      position: {x: -0, y: 0.171266, z: 0}
      rotation: {x: -0.09999194, y: 0.112348855, z: 0.019430157, w: 0.988434}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightForeArm
      parentName: mixamorig:RightArm
      position: {x: -0, y: 0.195964, z: 0}
      rotation: {x: -0.009200773, y: -0.06647565, z: 0.026046736, w: 0.9974056}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHand
      parentName: mixamorig:RightForeArm
      position: {x: -0, y: 0.271393, z: 0}
      rotation: {x: -0.012345989, y: 0.113161616, z: -0.10672643, w: 0.98775077}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb1
      parentName: mixamorig:RightHand
      position: {x: -0.050345, y: 0.041571997, z: -0.00083}
      rotation: {x: -0.0322901, y: 0.09731671, z: 0.43131652, w: 0.89635533}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb2
      parentName: mixamorig:RightHandThumb1
      position: {x: 0.012534999, y: 0.05071, z: 0}
      rotation: {x: 0.020739766, y: 0.025793986, z: -0.28205562, w: 0.958827}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb3
      parentName: mixamorig:RightHandThumb2
      position: {x: -0.014167, y: 0.053583, z: 0}
      rotation: {x: -0.03245858, y: 0.0043971143, z: 0.14998466, w: 0.9881456}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb4
      parentName: mixamorig:RightHandThumb3
      position: {x: 0.001632, y: 0.042704, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex1
      parentName: mixamorig:RightHand
      position: {x: -0.057034, y: 0.142399, z: -0.000206}
      rotation: {x: 0.08182254, y: 0.01324647, z: -0.04560798, w: 0.9955147}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex2
      parentName: mixamorig:RightHandIndex1
      position: {x: -0.000088999994, y: 0.055194, z: 0}
      rotation: {x: 0.026481094, y: 0.000031687312, z: -0.0025529943, w: 0.99964607}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex3
      parentName: mixamorig:RightHandIndex2
      position: {x: -0.000055999997, y: 0.054178998, z: 0}
      rotation: {x: 0.039761223, y: 0.000014593599, z: -0.0016398929, w: 0.99920785}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex4
      parentName: mixamorig:RightHandIndex3
      position: {x: 0.000145, y: 0.034411, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle1
      parentName: mixamorig:RightHand
      position: {x: 0.0029570002, y: 0.15360199, z: 0.002413}
      rotation: {x: 0.08253218, y: 0.013884595, z: -0.03358451, w: 0.99592566}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle2
      parentName: mixamorig:RightHandMiddle1
      position: {x: -0.000028999999, y: 0.049721997, z: 0}
      rotation: {x: 0.029612727, y: -0.00000474788, z: -0.0030717812, w: 0.9995567}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle3
      parentName: mixamorig:RightHandMiddle2
      position: {x: -0.000004, y: 0.052617, z: 0}
      rotation: {x: 0.040308934, y: -0.0112899775, z: -0.0067804214, w: 0.99910045}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle4
      parentName: mixamorig:RightHandMiddle3
      position: {x: 0.000033, y: 0.03383, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing1
      parentName: mixamorig:RightHand
      position: {x: 0.054077, y: 0.13907099, z: 0.001176}
      rotation: {x: 0.08204872, y: 0.012952073, z: -0.016529897, w: 0.9964071}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing2
      parentName: mixamorig:RightHandRing1
      position: {x: 0.000228, y: 0.041961003, z: 0}
      rotation: {x: 0.032944247, y: 0.00005195846, z: -0.006041515, w: 0.999439}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing3
      parentName: mixamorig:RightHandRing2
      position: {x: -0.000031, y: 0.041222, z: 0}
      rotation: {x: 0.039784662, y: -0.013577035, z: -0.0111140385, w: 0.9990542}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing4
      parentName: mixamorig:RightHandRing3
      position: {x: -0.000197, y: 0.018531999, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftUpLeg
      parentName: mixamorig:Hips
      position: {x: -0.238651, y: -0.050233997, z: 0.004012}
      rotation: {x: -0.034201156, y: 0.06949495, z: 0.99615365, w: 0.04097121}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftLeg
      parentName: mixamorig:LeftUpLeg
      position: {x: -0, y: 0.35845798, z: 0}
      rotation: {x: -0.31764847, y: 0.072575144, z: -0.017302878, w: 0.94526875}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftFoot
      parentName: mixamorig:LeftLeg
      position: {x: -0, y: 0.249197, z: 0}
      rotation: {x: 0.596011, y: -0.013480429, z: 0.038999006, w: 0.80191535}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToeBase
      parentName: mixamorig:LeftFoot
      position: {x: -0, y: 0.22541, z: 0}
      rotation: {x: 0.38063836, y: 0.021922408, z: -0.0092478795, w: 0.92441785}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToe_End
      parentName: mixamorig:LeftToeBase
      position: {x: -0, y: 0.10392, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightUpLeg
      parentName: mixamorig:Hips
      position: {x: 0.238651, y: -0.050233997, z: 0.002053}
      rotation: {x: 0.10735413, y: 0.071153976, z: 0.9897557, w: -0.0616108}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightLeg
      parentName: mixamorig:RightUpLeg
      position: {x: -0, y: 0.35842997, z: 0}
      rotation: {x: -0.3166506, y: -0.025698267, z: 0.014027191, w: 0.9480903}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightFoot
      parentName: mixamorig:RightLeg
      position: {x: -0, y: 0.25003698, z: 0}
      rotation: {x: 0.5884326, y: 0.0045720637, z: -0.058428433, w: 0.80641943}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToeBase
      parentName: mixamorig:RightFoot
      position: {x: -0, y: 0.22521299, z: 0}
      rotation: {x: 0.37795374, y: -0.021620551, z: 0.008829081, w: 0.9255299}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToe_End
      parentName: mixamorig:RightToeBase
      position: {x: -0, y: 0.104588, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
