using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }
    
    [Header("Selected Products")]
    public List<ProductData> selectedProducts = new List<ProductData>();
    
    [Header("Stage Management")]
    public int currentStage = 1;
    public int currentSubStage = 1;
    public Canvas[] stageCanvases = new Canvas[4];
    public float fadeDuration = 0.5f;

    [Header("Sub-Stage Configuration")]
    [Tooltip("Maximum sub-stages for each primary stage")]
    public int[] maxSubStagesPerStage = new int[] { 3, 4, 2, 3 }; // Stage 1: 3 sub-stages, Stage 2: 4 sub-stages, etc.

    [Header("Animation Integration")]
    public UIAnimationController animationController;
    
    private CanvasGroup[] canvasGroups;
    private bool isTransitioning = false;
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeCanvases();

            // Auto-find animation controller if not assigned
            if (animationController == null)
            {
                animationController = FindObjectOfType<UIAnimationController>();
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void InitializeCanvases()
    {
        canvasGroups = new CanvasGroup[stageCanvases.Length];
        
        for (int i = 0; i < stageCanvases.Length; i++)
        {
            if (stageCanvases[i] != null)
            {
                canvasGroups[i] = stageCanvases[i].GetComponent<CanvasGroup>();
                if (canvasGroups[i] == null)
                    canvasGroups[i] = stageCanvases[i].gameObject.AddComponent<CanvasGroup>();
                
                canvasGroups[i].alpha = (i == 0) ? 1f : 0f;
                stageCanvases[i].gameObject.SetActive(i == 0);
            }
        }
    }
    
    public void SelectProduct(ProductData product)
    {
        if (!selectedProducts.Contains(product))
        {
            selectedProducts.Add(product);
            Debug.Log($"Product selected: {product.Name}");
        }
        else
        {
            Debug.Log($"Product {product.Name} already selected");
        }
    }
    
    public void DeselectProduct(ProductData product)
    {
        if (selectedProducts.Remove(product))
        {
            Debug.Log($"Product deselected: {product.Name}");
        }
    }
    
    public bool IsProductSelected(ProductData product)
    {
        return selectedProducts.Contains(product);
    }
    
    public void ClearSelectedProducts()
    {
        selectedProducts.Clear();
        Debug.Log("All selected products cleared");
    }
    
    public void SetStage(int stage)
    {
        if (isTransitioning || stage == currentStage || stage < 1 || stage > stageCanvases.Length)
            return;

        StartCoroutine(TransitionToStage(stage));
    }

    public void SetSubStage(int subStage)
    {
        if (isTransitioning || subStage == currentSubStage || subStage < 1)
            return;

        // Check if sub-stage is valid for current primary stage
        int maxSubStages = GetMaxSubStagesForCurrentStage();
        if (subStage > maxSubStages)
        {
            Debug.LogWarning($"Sub-stage {subStage} exceeds maximum ({maxSubStages}) for stage {currentStage}");
            return;
        }

        int oldSubStage = currentSubStage;
        currentSubStage = subStage;

        // Trigger sub-stage change animations
        if (animationController != null)
        {
            animationController.TriggerSubStageChangeAnimations(currentStage, oldSubStage, currentSubStage);
        }

        Debug.Log($"Sub-stage changed from {oldSubStage} to {currentSubStage} (Stage {currentStage})");
    }

    public void SetStageAndSubStage(int stage, int subStage)
    {
        if (stage != currentStage)
        {
            // Primary stage change - reset sub-stage to 1 first
            currentSubStage = 1;
            SetStage(stage);
        }

        // Then set the sub-stage
        SetSubStage(subStage);
    }

    public int GetMaxSubStagesForCurrentStage()
    {
        if (currentStage > 0 && currentStage <= maxSubStagesPerStage.Length)
        {
            return maxSubStagesPerStage[currentStage - 1];
        }
        return 1; // Default to 1 sub-stage if not configured
    }

    public int GetMaxSubStagesForStage(int stage)
    {
        if (stage > 0 && stage <= maxSubStagesPerStage.Length)
        {
            return maxSubStagesPerStage[stage - 1];
        }
        return 1; // Default to 1 sub-stage if not configured
    }

    public string GetCurrentStageString()
    {
        return $"Stage {currentStage}.{currentSubStage}";
    }
    
    IEnumerator TransitionToStage(int newStage)
    {
        isTransitioning = true;
        int oldStage = currentStage;
        
        if (stageCanvases[newStage - 1] != null)
            stageCanvases[newStage - 1].gameObject.SetActive(true);
        
        float elapsed = 0f;
        while (elapsed < fadeDuration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / fadeDuration;
            
            if (canvasGroups[oldStage - 1] != null)
                canvasGroups[oldStage - 1].alpha = 1f - t;
                
            if (canvasGroups[newStage - 1] != null)
                canvasGroups[newStage - 1].alpha = t;
                
            yield return null;
        }
        
        if (canvasGroups[oldStage - 1] != null)
        {
            canvasGroups[oldStage - 1].alpha = 0f;
            stageCanvases[oldStage - 1].gameObject.SetActive(false);
        }
        
        if (canvasGroups[newStage - 1] != null)
            canvasGroups[newStage - 1].alpha = 1f;
        
        currentStage = newStage;
        currentSubStage = 1; // Reset to sub-stage 1 when changing primary stage
        isTransitioning = false;

        // Trigger stage change animations
        if (animationController != null)
        {
            animationController.TriggerStageChangeAnimations(oldStage, newStage);
        }

        Debug.Log($"Stage changed to: {newStage}.{currentSubStage}");
    }
    
    [ContextMenu("Print Selected Products")]
    public void PrintSelectedProducts()
    {
        Debug.Log("--- Selected Products ---");
        if (selectedProducts.Count == 0)
        {
            Debug.Log("No products selected");
        }
        else
        {
            for (int i = 0; i < selectedProducts.Count; i++)
            {
                var product = selectedProducts[i];
                Debug.Log($"{i + 1}. Name: {product.Name}, Price: {product.Price}, Info: {product.Info}");
            }
        }
        Debug.Log("------------------------");
    }

    [ContextMenu("Print Current Stage Info")]
    public void PrintCurrentStageInfo()
    {
        Debug.Log($"Current Stage: {GetCurrentStageString()}");
        Debug.Log($"Max Sub-Stages for Stage {currentStage}: {GetMaxSubStagesForCurrentStage()}");
    }

    [ContextMenu("Test: Go to Stage 1.2")]
    public void TestGoToStage1Sub2()
    {
        SetStageAndSubStage(1, 2);
    }

    [ContextMenu("Test: Go to Stage 2.1")]
    public void TestGoToStage2Sub1()
    {
        SetStageAndSubStage(2, 1);
    }

    [ContextMenu("Test: Next Sub-Stage")]
    public void TestNextSubStage()
    {
        int nextSubStage = currentSubStage + 1;
        if (nextSubStage <= GetMaxSubStagesForCurrentStage())
        {
            SetSubStage(nextSubStage);
        }
        else
        {
            Debug.Log($"Already at maximum sub-stage ({currentSubStage}) for stage {currentStage}");
        }
    }

    [ContextMenu("Test: Previous Sub-Stage")]
    public void TestPreviousSubStage()
    {
        int prevSubStage = currentSubStage - 1;
        if (prevSubStage >= 1)
        {
            SetSubStage(prevSubStage);
        }
        else
        {
            Debug.Log($"Already at minimum sub-stage (1) for stage {currentStage}");
        }
    }
}