%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MicAnim
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - serializedVersion: 2
    curve:
    - time: 0.083333336
      value: {fileID: 21300000, guid: 87727346fdf5d1d4a8c69c721e2e730b, type: 3}
    - time: 0.16666667
      value: {fileID: 21300000, guid: 96f8fac98229a6b4db753bac00ae23f3, type: 3}
    - time: 0.25
      value: {fileID: 21300000, guid: 597935bb8e0e23f4385c25d54ee1a138, type: 3}
    - time: 0.33333334
      value: {fileID: 21300000, guid: 28a1afe6e68f67e4d8d2b3172b9fb0d6, type: 3}
    - time: 0.41666666
      value: {fileID: 21300000, guid: 28a1afe6e68f67e4d8d2b3172b9fb0d6, type: 3}
    attribute: m_Sprite
    path: 
    classID: 114
    script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
    flags: 2
  m_SampleRate: 12
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2015549526
      script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 1
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping:
    - {fileID: 21300000, guid: 87727346fdf5d1d4a8c69c721e2e730b, type: 3}
    - {fileID: 21300000, guid: 96f8fac98229a6b4db753bac00ae23f3, type: 3}
    - {fileID: 21300000, guid: 597935bb8e0e23f4385c25d54ee1a138, type: 3}
    - {fileID: 21300000, guid: 28a1afe6e68f67e4d8d2b3172b9fb0d6, type: 3}
    - {fileID: 21300000, guid: 28a1afe6e68f67e4d8d2b3172b9fb0d6, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 1
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
