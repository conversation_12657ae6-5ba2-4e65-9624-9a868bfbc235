using UnityEngine;
using System.Linq;

public class ContextualButtonTestGuide : MonoBehaviour
{
    [Header("References")]
    public DetectionZonePanelManager panelManager;
    
    void Start()
    {
        if (panelManager == null)
        {
            panelManager = FindObjectOfType<DetectionZonePanelManager>();
        }
    }
    
    [ContextMenu("Test Complete Flow")]
    public void TestCompleteFlow()
    {
        if (panelManager == null)
        {
            Debug.LogError("DetectionZonePanelManager not found!");
            return;
        }
        
        Debug.Log("=== TESTING COMPLETE CONTEXTUAL BUTTON FLOW ===");
        
        // Step 1: Create content in right zone to make button appear
        CreateContentInRightZone();
        
        // Step 2: Check if button is visible
        CheckButtonVisibility();
        
        // Step 3: Simulate button click
        SimulateButtonClick();
        
        // Step 4: Check if left detection zone is active and visible
        CheckLeftDetectionZoneStatus();
        
        Debug.Log("=== TEST COMPLETE ===");
    }
    
    [ContextMenu("Create Content in Right Zone")]
    public void CreateContentInRightZone()
    {
        var zones = panelManager.detectionZone?.detectionZones?.OrderBy(z => z.x).ToList();
        if (zones == null || zones.Count < 2)
        {
            Debug.LogError("Need at least 2 zones for this test!");
            return;
        }
        
        string rightZone = zones[1].zoneName;
        
        var testProduct = new ProductData
        {
            Name = "Test Product for Right Zone",
            Price = 99,
            Info = "This should trigger the contextual button"
        };
        
        Debug.Log($"Creating test content in right zone: '{rightZone}'");
        panelManager.ShowProductInZone(rightZone, testProduct);
        Debug.Log("✅ Content created in right zone");
    }
    
    [ContextMenu("Check Button Visibility")]
    public void CheckButtonVisibility()
    {
        var buttonContainer = GameObject.Find("LeftPanelButtonContainer");
        if (buttonContainer != null)
        {
            bool isVisible = buttonContainer.activeInHierarchy;
            Debug.Log($"✅ Contextual button visible: {isVisible}");
            
            if (!isVisible)
            {
                Debug.LogWarning("❌ Button should be visible but it's not!");
            }
        }
        else
        {
            Debug.LogError("❌ LeftPanelButtonContainer not found!");
        }
    }
    
    [ContextMenu("Simulate Button Click")]
    public void SimulateButtonClick()
    {
        if (panelManager?.contextualButton != null)
        {
            Debug.Log("🖱️ Simulating button click...");
            
            // Enable debug logging to see what happens
            panelManager.contextualButton.showDebugInfo = true;
            
            // Simulate the button click
            panelManager.ToggleLeftPanel();
            
            Debug.Log("✅ Button click simulated");
        }
        else
        {
            Debug.LogError("❌ Contextual button not found!");
        }
    }
    
    [ContextMenu("Check Left Detection Zone Status")]
    public void CheckLeftDetectionZoneStatus()
    {
        if (panelManager?.detectionZone == null)
        {
            Debug.LogError("❌ VuforiaDetectionZone not found!");
            return;
        }
        
        bool isActive = panelManager.detectionZone.IsLeftDetectionZoneActive();
        string leftZoneName = panelManager.GetLeftZoneName();
        
        Debug.Log($"✅ Left detection zone '{leftZoneName}' active: {isActive}");
        
        if (isActive)
        {
            Debug.Log("✅ Left detection zone should now be visible with OnGUI drawing!");
            
            // Check if the zone has showVisualBounds enabled
            var leftZone = panelManager.detectionZone.detectionZones?.FirstOrDefault(z => z.zoneName == leftZoneName);
            if (leftZone != null)
            {
                Debug.Log($"✅ Left zone showVisualBounds: {leftZone.showVisualBounds}");
                Debug.Log($"✅ Left zone color: {leftZone.zoneColor}");
                
                if (leftZone.showVisualBounds && leftZone.isActive)
                {
                    Debug.Log("🎉 LEFT DETECTION ZONE SHOULD BE DRAWN WITH ONGUI!");
                }
                else
                {
                    Debug.LogWarning("❌ Zone won't be drawn - showVisualBounds or isActive is false");
                }
            }
        }
        else
        {
            Debug.LogWarning("❌ Left detection zone is not active!");
        }
    }
    
    [ContextMenu("Clear All Content")]
    public void ClearAllContent()
    {
        if (panelManager != null)
        {
            panelManager.ClearAllPanelDataForTesting();
            Debug.Log("✅ All content cleared - button should now be hidden");
        }
    }
}
