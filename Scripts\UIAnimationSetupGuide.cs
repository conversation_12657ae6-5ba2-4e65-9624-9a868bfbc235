using DhafinFawwaz.AnimationUILib;
using UnityEngine;

/// <summary>
/// Setup guide and helper for the UIAnimationController system.
/// This script provides context menu options to help configure the animation system.
/// </summary>
public class UIAnimationSetupGuide : MonoBehaviour
{
    [Header("Setup Instructions")]
    [TextArea(10, 20)]
    public string setupInstructions = @"
🎯 UI ANIMATION CONTROLLER SETUP GUIDE

1. CREATE ANIMATION CONTROLLER:
   - Create empty GameObject
   - Add UIAnimationController script
   - This script will auto-find DetectionZonePanelManager and GameManager

2. CONFIGURE YOUR ANIMATIONS:
   - Create your AnimationUI GameObjects with animations configured
   - In UIAnimationController, set Animation Triggers array size
   - Drag your AnimationUI objects into the array

3. CONFIGURE TRIGGER CONDITIONS:
   For each Animation Trigger, set:
   ✓ Animation UI: [Your AnimationUI GameObject]
   ✓ Trigger conditions (VuMark detection, search results, etc.)
   ✓ Zone filtering (optional - target specific zones)
   ✓ Animation direction (Play Forward/Reverse)
   ✓ Delay (optional)

4. EXAMPLE CONFIGURATIONS:

   LINE ANIMATION ON RIGHT PANEL CONTENT:
   - Trigger On VuMark Detection: ✓
   - Trigger On Search Results: ✓
   - Filter By Zone: ✓
   - Target Zone Name: 'Right Detection Zone'
   - Play Forward: ✓
   - Delay: 0.2s

   REVERSE ANIMATION ON CONTENT CLEAR:
   - Trigger On VuMark Lost: ✓
   - Trigger On Search Cleared: ✓
   - Play Reverse: ✓
   - Delay: 0s

5. UNITY EVENTS (Optional):
   Use the UnityEvents in UIAnimationController for additional custom triggers:
   - OnVuMarkDetected
   - OnSearchResultsShown
   - OnLeftPanelShown
   - OnStageChanged
   - OnSubStageChanged
   - etc.

6. SUB-STAGE CONFIGURATION:
   For stage-based animations, you can now target:
   - Primary stages (1, 2, 3, 4)
   - Sub-stages within each primary stage (1.1, 1.2, 1.3, etc.)

   Example Sub-Stage Configurations:
   - Target Stage: 1, Target Sub-Stage: 2 (triggers on Stage 1.2)
   - Target Stage: 2, Trigger On Any Sub-Stage: ✓ (triggers on any Stage 2.x)
   - Trigger On Sub-Stage Change: ✓ (triggers only on sub-stage changes, not primary stage changes)

7. TESTING:
   Use the context menu options on UIAnimationController:
   - Test VuMark Detection Animation
   - Test Search Results Animation
   - Test Left Panel Show/Hide Animation
   - Test Stage Change Animation
   - Test Sub-Stage Change Animation
";

    [Header("Quick Setup")]
    public UIAnimationController animationController;
    public AnimationUI[] availableAnimations;

    [ContextMenu("Find Animation Controller")]
    public void FindAnimationController()
    {
        animationController = FindObjectOfType<UIAnimationController>();
        if (animationController != null)
        {
            Debug.Log($"✅ Found UIAnimationController on '{animationController.name}'");
        }
        else
        {
            Debug.LogWarning("❌ No UIAnimationController found in scene. Create one first!");
        }
    }

    [ContextMenu("Find All AnimationUI Objects")]
    public void FindAllAnimationUIObjects()
    {
        var foundAnimations = FindObjectsOfType<AnimationUI>();
        availableAnimations = foundAnimations;
        
        Debug.Log($"✅ Found {foundAnimations.Length} AnimationUI objects:");
        foreach (var anim in foundAnimations)
        {
            Debug.Log($"   - {anim.name}");
        }
    }

    [ContextMenu("Validate Setup")]
    public void ValidateSetup()
    {
        Debug.Log("🔍 VALIDATING UI ANIMATION SETUP...");
        
        // Check for UIAnimationController
        var controller = FindObjectOfType<UIAnimationController>();
        if (controller == null)
        {
            Debug.LogError("❌ No UIAnimationController found! Create one first.");
            return;
        }
        Debug.Log("✅ UIAnimationController found");

        // Check for DetectionZonePanelManager integration
        var panelManager = FindObjectOfType<DetectionZonePanelManager>();
        if (panelManager == null)
        {
            Debug.LogWarning("⚠️ No DetectionZonePanelManager found");
        }
        else if (panelManager.animationController == null)
        {
            Debug.LogWarning("⚠️ DetectionZonePanelManager.animationController is not assigned");
        }
        else
        {
            Debug.Log("✅ DetectionZonePanelManager integration configured");
        }

        // Check for GameManager integration
        if (GameManager.Instance == null)
        {
            Debug.LogWarning("⚠️ No GameManager found");
        }
        else if (GameManager.Instance.animationController == null)
        {
            Debug.LogWarning("⚠️ GameManager.animationController is not assigned");
        }
        else
        {
            Debug.Log("✅ GameManager integration configured");
        }

        // Check animation triggers
        if (controller.animationTriggers == null || controller.animationTriggers.Length == 0)
        {
            Debug.LogWarning("⚠️ No animation triggers configured in UIAnimationController");
        }
        else
        {
            Debug.Log($"✅ {controller.animationTriggers.Length} animation triggers configured");

            int validTriggers = 0;
            int stageBasedTriggers = 0;
            int subStageBasedTriggers = 0;

            foreach (var trigger in controller.animationTriggers)
            {
                if (trigger.animationUI != null)
                {
                    validTriggers++;
                }

                if (trigger.triggerOnStageChange)
                {
                    stageBasedTriggers++;
                }

                if (trigger.triggerOnSubStageChange)
                {
                    subStageBasedTriggers++;
                }
            }

            Debug.Log($"✅ {validTriggers} triggers have valid AnimationUI references");
            Debug.Log($"✅ {stageBasedTriggers} stage-based triggers configured");
            Debug.Log($"✅ {subStageBasedTriggers} sub-stage-based triggers configured");
        }

        // Check sub-stage configuration in GameManager
        if (GameManager.Instance != null)
        {
            Debug.Log($"✅ Current stage: {GameManager.Instance.GetCurrentStageString()}");
            Debug.Log($"✅ Max sub-stages per stage: [{string.Join(", ", GameManager.Instance.maxSubStagesPerStage)}]");
        }

        Debug.Log("🎉 Setup validation complete!");
    }

    [ContextMenu("Create Sample Animation Trigger")]
    public void CreateSampleAnimationTrigger()
    {
        if (animationController == null)
        {
            Debug.LogError("❌ No animation controller assigned. Use 'Find Animation Controller' first.");
            return;
        }

        Debug.Log(@"
📝 SAMPLE ANIMATION TRIGGER CONFIGURATION:

1. In UIAnimationController, increase 'Animation Triggers' array size by 1
2. Configure the new trigger with these settings:

   EXAMPLE: Line Animation on Right Panel Content
   - Animation UI: [Drag your line AnimationUI GameObject here]
   - Trigger On VuMark Detection: ✓
   - Trigger On Search Results: ✓
   - Filter By Zone: ✓
   - Target Zone Name: 'Right Detection Zone'
   - Play Forward: ✓
   - Delay Before Play: 0.2

3. Test using the context menu: 'Test VuMark Detection Animation'
");
    }
}
