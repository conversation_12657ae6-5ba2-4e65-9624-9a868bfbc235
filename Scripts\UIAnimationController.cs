using UnityEngine;
using UnityEngine.Events;
using System.Collections;
using DhafinFawwaz.AnimationUILib;

[System.Serializable]
public class AnimationTrigger
{
    [Header("Animation Reference")]
    public AnimationUI animationUI;
    
    [Header("Trigger Conditions")]
    public bool triggerOnVuMarkDetection = true;
    public bool triggerOnVuMarkLost = false;
    public bool triggerOnSearchResults = true;
    public bool triggerOnSearchCleared = false;
    public bool triggerOnLeftPanelShow = false;
    public bool triggerOnLeftPanelHide = false;
    public bool triggerOnStageChange = false;
    
    [Header("Zone Filtering")]
    public bool filterByZone = false;
    public string targetZoneName = "";
    
    [Header("Animation Direction")]
    public bool playForward = true;
    public bool playReverse = false;
    
    [Header("Timing")]
    public float delayBeforePlay = 0f;
    
    [Header("Stage Filtering (if triggerOnStageChange is true)")]
    public int targetStage = 1;
    public int targetSubStage = 1;
    public bool triggerOnSubStageChange = false;
    public bool triggerOnAnySubStage = false; // If true, triggers on any sub-stage within the target stage
}

public enum AnimationTriggerType
{
    VuMarkDetection,
    VuMarkLost,
    SearchResults,
    SearchCleared,
    LeftPanelShow,
    LeftPanelHide,
    StageChange,
    SubStageChange
}

public class UIAnimationController : MonoBehaviour
{
    [Header("Animation Triggers")]
    public AnimationTrigger[] animationTriggers;
    
    [Header("References")]
    public DetectionZonePanelManager panelManager;
    public GameManager gameManager;
    
    [Header("Debug")]
    public bool enableDebugLogging = false;

    [Header("Safety Settings")]
    [Tooltip("Minimum time between animation triggers for the same AnimationUI (prevents rapid-fire conflicts)")]
    public float minTimeBetweenTriggers = 0.1f;

    // Track last trigger times for each AnimationUI to prevent conflicts
    private System.Collections.Generic.Dictionary<AnimationUI, float> lastTriggerTimes = new System.Collections.Generic.Dictionary<AnimationUI, float>();
    
    [Header("Unity Events (Designer Friendly)")]
    public UnityEvent OnVuMarkDetected;
    public UnityEvent OnVuMarkLost;
    public UnityEvent OnSearchResultsShown;
    public UnityEvent OnSearchResultsCleared;
    public UnityEvent OnLeftPanelShown;
    public UnityEvent OnLeftPanelHidden;
    public UnityEvent OnStageChanged;
    public UnityEvent OnSubStageChanged;
    
    void Start()
    {
        // Auto-find references if not assigned
        if (panelManager == null)
            panelManager = FindObjectOfType<DetectionZonePanelManager>();
            
        if (gameManager == null)
            gameManager = GameManager.Instance;
            
        if (enableDebugLogging)
        {
            Debug.Log($"UIAnimationController: Initialized with {animationTriggers.Length} animation triggers");
        }
    }
    
    #region Public Trigger Methods
    
    public void TriggerVuMarkDetectedAnimations(string zoneName = "")
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: VuMark detected in zone '{zoneName}'");
            
        OnVuMarkDetected?.Invoke();
        TriggerAnimations(AnimationTriggerType.VuMarkDetection, zoneName);
    }
    
    public void TriggerVuMarkLostAnimations(string zoneName = "")
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: VuMark lost in zone '{zoneName}'");
            
        OnVuMarkLost?.Invoke();
        TriggerAnimations(AnimationTriggerType.VuMarkLost, zoneName);
    }
    
    public void TriggerSearchResultsAnimations(string zoneName = "")
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: Search results shown in zone '{zoneName}'");
            
        OnSearchResultsShown?.Invoke();
        TriggerAnimations(AnimationTriggerType.SearchResults, zoneName);
    }
    
    public void TriggerSearchClearedAnimations(string zoneName = "")
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: Search cleared in zone '{zoneName}'");
            
        OnSearchResultsCleared?.Invoke();
        TriggerAnimations(AnimationTriggerType.SearchCleared, zoneName);
    }
    
    public void TriggerLeftPanelAnimations(bool isShowing)
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: Left panel {(isShowing ? "shown" : "hidden")}");
            
        if (isShowing)
        {
            OnLeftPanelShown?.Invoke();
            TriggerAnimations(AnimationTriggerType.LeftPanelShow, "");
        }
        else
        {
            OnLeftPanelHidden?.Invoke();
            TriggerAnimations(AnimationTriggerType.LeftPanelHide, "");
        }
    }
    
    public void TriggerStageChangeAnimations(int oldStage, int newStage)
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: Stage changed from {oldStage} to {newStage}");

        OnStageChanged?.Invoke();
        TriggerAnimationsForStage(newStage, 1); // Default to sub-stage 1 for primary stage changes
    }

    public void TriggerSubStageChangeAnimations(int stage, int oldSubStage, int newSubStage)
    {
        if (enableDebugLogging)
            Debug.Log($"UIAnimationController: Sub-stage changed from {stage}.{oldSubStage} to {stage}.{newSubStage}");

        OnSubStageChanged?.Invoke();
        TriggerAnimationsForSubStage(stage, newSubStage);
    }
    
    #endregion
    
    #region Animation Triggering Logic
    
    void TriggerAnimations(AnimationTriggerType triggerType, string zoneName)
    {
        foreach (var trigger in animationTriggers)
        {
            if (ShouldTriggerAnimation(trigger, triggerType, zoneName))
            {
                StartCoroutine(PlayAnimationWithDelay(trigger, triggerType.ToString()));
            }
        }
    }
    
    void TriggerAnimationsForStage(int stage, int subStage)
    {
        foreach (var trigger in animationTriggers)
        {
            if (ShouldTriggerAnimationForStage(trigger, stage, subStage))
            {
                StartCoroutine(PlayAnimationWithDelay(trigger, $"Stage{stage}.{subStage}"));
            }
        }
    }

    void TriggerAnimationsForSubStage(int stage, int subStage)
    {
        foreach (var trigger in animationTriggers)
        {
            if (ShouldTriggerAnimationForSubStage(trigger, stage, subStage))
            {
                StartCoroutine(PlayAnimationWithDelay(trigger, $"SubStage{stage}.{subStage}"));
            }
        }
    }
    
    bool ShouldTriggerAnimation(AnimationTrigger trigger, AnimationTriggerType triggerType, string zoneName)
    {
        if (trigger.animationUI == null) return false;
        
        // Check trigger conditions
        bool shouldTrigger = false;
        
        switch (triggerType)
        {
            case AnimationTriggerType.VuMarkDetection:
                shouldTrigger = trigger.triggerOnVuMarkDetection;
                break;
            case AnimationTriggerType.VuMarkLost:
                shouldTrigger = trigger.triggerOnVuMarkLost;
                break;
            case AnimationTriggerType.SearchResults:
                shouldTrigger = trigger.triggerOnSearchResults;
                break;
            case AnimationTriggerType.SearchCleared:
                shouldTrigger = trigger.triggerOnSearchCleared;
                break;
            case AnimationTriggerType.LeftPanelShow:
                shouldTrigger = trigger.triggerOnLeftPanelShow;
                break;
            case AnimationTriggerType.LeftPanelHide:
                shouldTrigger = trigger.triggerOnLeftPanelHide;
                break;
        }
        
        // Check zone filtering
        if (shouldTrigger && trigger.filterByZone && !string.IsNullOrEmpty(trigger.targetZoneName))
        {
            shouldTrigger = trigger.targetZoneName.Equals(zoneName, System.StringComparison.OrdinalIgnoreCase);
        }
        
        return shouldTrigger;
    }
    
    bool ShouldTriggerAnimationForStage(AnimationTrigger trigger, int stage, int subStage)
    {
        if (trigger.animationUI == null) return false;

        if (!trigger.triggerOnStageChange) return false;

        if (trigger.targetStage != stage) return false;

        // For primary stage changes, only trigger if it's the right sub-stage or if it triggers on any sub-stage
        return trigger.triggerOnAnySubStage || trigger.targetSubStage == subStage;
    }

    bool ShouldTriggerAnimationForSubStage(AnimationTrigger trigger, int stage, int subStage)
    {
        if (trigger.animationUI == null) return false;

        if (!trigger.triggerOnSubStageChange) return false;

        if (trigger.targetStage != stage) return false;

        return trigger.targetSubStage == subStage;
    }
    
    IEnumerator PlayAnimationWithDelay(AnimationTrigger trigger, string triggerSource)
    {
        if (trigger.delayBeforePlay > 0)
        {
            if (enableDebugLogging)
                Debug.Log($"UIAnimationController: Waiting {trigger.delayBeforePlay}s before playing animation for {triggerSource}");

            yield return new WaitForSeconds(trigger.delayBeforePlay);
        }

        if (trigger.animationUI != null)
        {
            try
            {
                if (trigger.playForward)
                {
                    if (enableDebugLogging)
                        Debug.Log($"UIAnimationController: Playing forward animation '{trigger.animationUI.name}' for {triggerSource}");

                    // Check if the AnimationUI is ready before playing
                    if (IsAnimationUIReady(trigger.animationUI))
                    {
                        // Try to use AnimationUIHelper for safer playback
                        var helper = trigger.animationUI.GetComponent<AnimationUIHelper>();
                        if (helper != null)
                        {
                            // Use simple safe play to avoid complex reflection issues
                            helper.SimpleSafePlay();
                        }
                        else
                        {
                            // Direct call with error handling
                            try
                            {
                                trigger.animationUI.Play();
                            }
                            catch (System.Exception e)
                            {
                                if (enableDebugLogging)
                                    Debug.LogWarning($"UIAnimationController: Animation play failed for '{trigger.animationUI.name}': {e.Message}");
                            }
                        }
                    }
                    else
                    {
                        if (enableDebugLogging)
                            Debug.LogWarning($"UIAnimationController: AnimationUI '{trigger.animationUI.name}' is not ready for playback");
                    }
                }
                else if (trigger.playReverse)
                {
                    if (enableDebugLogging)
                        Debug.Log($"UIAnimationController: Playing reverse animation '{trigger.animationUI.name}' for {triggerSource}");

                    // Check if the AnimationUI is ready before playing
                    if (IsAnimationUIReady(trigger.animationUI))
                    {
                        // Try to use AnimationUIHelper for safer playback
                        var helper = trigger.animationUI.GetComponent<AnimationUIHelper>();
                        if (helper != null)
                        {
                            // Use simple safe play to avoid complex reflection issues
                            helper.SimpleSafePlayReversed();
                        }
                        else
                        {
                            // Direct call with error handling
                            try
                            {
                                trigger.animationUI.PlayReversed();
                            }
                            catch (System.Exception e)
                            {
                                if (enableDebugLogging)
                                    Debug.LogWarning($"UIAnimationController: Animation play reversed failed for '{trigger.animationUI.name}': {e.Message}");
                            }
                        }
                    }
                    else
                    {
                        if (enableDebugLogging)
                            Debug.LogWarning($"UIAnimationController: AnimationUI '{trigger.animationUI.name}' is not ready for reverse playback");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"UIAnimationController: Error playing animation '{trigger.animationUI.name}' for {triggerSource}: {e.Message}");
            }
        }
    }

    /// <summary>
    /// Check if AnimationUI is ready for playback (helps prevent null coroutine errors)
    /// </summary>
    private bool IsAnimationUIReady(AnimationUI animationUI)
    {
        if (animationUI == null) return false;

        // Check if the GameObject is active
        if (!animationUI.gameObject.activeInHierarchy) return false;

        // Check if the AnimationUI component is enabled
        if (!animationUI.enabled) return false;

        // Check if enough time has passed since last trigger (prevent rapid-fire conflicts)
        if (lastTriggerTimes.ContainsKey(animationUI))
        {
            float timeSinceLastTrigger = Time.time - lastTriggerTimes[animationUI];
            if (timeSinceLastTrigger < minTimeBetweenTriggers)
            {
                if (enableDebugLogging)
                    Debug.Log($"UIAnimationController: Skipping animation '{animationUI.name}' - too soon since last trigger ({timeSinceLastTrigger:F2}s < {minTimeBetweenTriggers:F2}s)");
                return false;
            }
        }

        // Update last trigger time
        lastTriggerTimes[animationUI] = Time.time;

        return true;
    }
    
    #endregion
    
    #region Context Menu Helpers
    
    [ContextMenu("Test VuMark Detection Animation")]
    public void TestVuMarkDetection()
    {
        TriggerVuMarkDetectedAnimations("Right Detection Zone");
    }
    
    [ContextMenu("Test Search Results Animation")]
    public void TestSearchResults()
    {
        TriggerSearchResultsAnimations("Right Detection Zone");
    }
    
    [ContextMenu("Test Left Panel Show Animation")]
    public void TestLeftPanelShow()
    {
        TriggerLeftPanelAnimations(true);
    }
    
    [ContextMenu("Test Left Panel Hide Animation")]
    public void TestLeftPanelHide()
    {
        TriggerLeftPanelAnimations(false);
    }

    [ContextMenu("Test Stage Change Animation")]
    public void TestStageChange()
    {
        TriggerStageChangeAnimations(1, 2);
    }

    [ContextMenu("Test Sub-Stage Change Animation")]
    public void TestSubStageChange()
    {
        TriggerSubStageChangeAnimations(1, 1, 2);
    }

    [ContextMenu("Reset Animation States")]
    public void ResetAnimationStates()
    {
        lastTriggerTimes.Clear();
        Debug.Log("UIAnimationController: Animation trigger times reset");
    }

    [ContextMenu("Debug Animation Status")]
    public void DebugAnimationStatus()
    {
        Debug.Log("=== ANIMATION STATUS DEBUG ===");
        Debug.Log($"Total animation triggers: {animationTriggers?.Length ?? 0}");
        Debug.Log($"Min time between triggers: {minTimeBetweenTriggers}s");
        Debug.Log($"Tracked animations: {lastTriggerTimes.Count}");

        if (animationTriggers != null)
        {
            for (int i = 0; i < animationTriggers.Length; i++)
            {
                var trigger = animationTriggers[i];
                if (trigger.animationUI != null)
                {
                    bool isReady = IsAnimationUIReady(trigger.animationUI);
                    Debug.Log($"  [{i}] {trigger.animationUI.name}: Ready={isReady}, Active={trigger.animationUI.gameObject.activeInHierarchy}, Enabled={trigger.animationUI.enabled}");
                }
                else
                {
                    Debug.Log($"  [{i}] NULL AnimationUI reference");
                }
            }
        }
        Debug.Log("=== END DEBUG ===");
    }
    
    #endregion
}
