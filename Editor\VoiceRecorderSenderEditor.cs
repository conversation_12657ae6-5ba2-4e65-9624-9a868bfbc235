using UnityEngine;
using UnityEditor; // Required for <PERSON>, CustomEditor, EditorGUILayout
using System.Linq; // Required for Linq extensions like ToArray()

// This attribute tells Unity to use this custom editor for the VoiceRecorderSender component.
[CustomEditor(typeof(VoiceRecorderSender))]
public class VoiceRecorderSenderEditor : Editor
{
    // Reference to the target script (VoiceRecorderSender instance)
    private VoiceRecorderSender myTarget;

    // Array to hold the names of available microphone devices
    private string[] microphoneDevices;
    // Index of the currently selected microphone in the dropdown
    private int selectedMicrophoneIndex = 0;

    void OnEnable()
    {
        // Get the VoiceRecorderSender instance that this editor is inspecting
        myTarget = (VoiceRecorderSender)target;

        // Get the list of available microphone devices
        microphoneDevices = Microphone.devices;

        // If no devices are found, add a placeholder
        if (microphoneDevices.Length == 0)
        {
            microphoneDevices = new string[] { "No Microphones Found" };
            selectedMicrophoneIndex = 0;
        }
        else
        {
            // Try to find the index of the currently selected device in the target script
            // If myTarget.selectedDevice is empty or not found, default to the first device.
            selectedMicrophoneIndex = System.Array.IndexOf(microphoneDevices, myTarget.selectedDevice);
            if (selectedMicrophoneIndex == -1)
            {
                selectedMicrophoneIndex = 0; // Default to the first device
                // Optionally, update the target script's selectedDevice to match the default
                if (microphoneDevices.Length > 0 && myTarget.selectedDevice != microphoneDevices[0])
                {
                    myTarget.selectedDevice = microphoneDevices[0];
                    EditorUtility.SetDirty(myTarget); // Mark as dirty to save changes
                }
            }
        }
    }

    public override void OnInspectorGUI()
    {
        // Draw the default inspector for all properties first (optional, but good for custom inspectors)
        // This ensures properties not explicitly drawn here still appear.
        DrawDefaultInspector();

        // --- Custom Microphone Selection Dropdown ---
        EditorGUILayout.Space(); // Add some space for better readability
        EditorGUILayout.LabelField("Microphone Selection", EditorStyles.boldLabel);

        // Check if there are actual microphones available
        if (microphoneDevices.Length > 0 && microphoneDevices[0] != "No Microphones Found")
        {
            // Create the dropdown (popup) in the Inspector
            int newIndex = EditorGUILayout.Popup("Select Microphone", selectedMicrophoneIndex, microphoneDevices);

            // If the selection has changed
            if (newIndex != selectedMicrophoneIndex)
            {
                selectedMicrophoneIndex = newIndex;
                // Update the selectedDevice property in the VoiceRecorderSender script
                myTarget.selectedDevice = microphoneDevices[selectedMicrophoneIndex];
                // Mark the target object as dirty so Unity saves the change
                EditorUtility.SetDirty(myTarget);
            }
        }
        else
        {
            // Display a message if no microphones are found
            EditorGUILayout.HelpBox("No microphone devices detected on your system.", MessageType.Warning);
        }

        EditorGUILayout.Space();
    }
}
